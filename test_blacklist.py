#!/usr/bin/env python3
"""
Test script to verify blacklist functionality
"""

import sys
import os

# Enable coordination before importing
os.environ['AUTOMATION_COORDINATION_ENABLED'] = 'true'

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from naukri_automation_original import CoordinationClient

def test_blacklist():
    print("🧪 Testing Blacklist Functionality")
    print("="*50)
    
    # Initialize coordination client
    client = CoordinationClient()
    
    # Test companies
    test_companies = [
        "Benovymed Healthcare Private Limited",
        "TCS",
        "Infosys",
        "Wipro"
    ]
    
    print("1. Pre-populating blacklist...")
    client.add_to_blacklist("Benovymed Healthcare Private Limited")
    
    print("\n2. Testing company application tracking...")
    for company in test_companies:
        print(f"\n   Testing: {company}")
        
        # Check if blacklisted
        is_blacklisted = client.is_company_blacklisted(company)
        print(f"   Initially blacklisted: {is_blacklisted}")
        
        if not is_blacklisted:
            # Simulate applications
            for i in range(3):  # Try 3 applications
                count, was_blacklisted = client.add_company_application(company)
                print(f"   Application {i+1}: count={count}, blacklisted={was_blacklisted}")
                
                if was_blacklisted:
                    break
    
    print("\n3. Final blacklist summary:")
    client.print_blacklist_summary()
    
    print("\n4. Testing blacklist checks:")
    for company in test_companies:
        is_blacklisted = client.is_company_blacklisted(company)
        print(f"   {company}: {'🚫 BLACKLISTED' if is_blacklisted else '✅ ALLOWED'}")

if __name__ == "__main__":
    test_blacklist()
