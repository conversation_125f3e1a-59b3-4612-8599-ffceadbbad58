# Naukri API Server

This directory contains the Flask-based API server that provides AI-powered responses for the Naukri job automation system.

## Features

- **GPT-2 Powered Responses**: Uses OpenAI's GPT-2 model for generating contextual answers
- **Domain-Specific Experience**: Handles technical questions about different domains (Data Science, ML, AI, etc.)
- **Personalized Information**: Responds with customized salary, location, education details
- **Environment Configuration**: Uses `.env` file for personalization

## Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Start the Server

**Option A: Python**
```bash
python server.py
```

**Option B: Windows Batch**
```cmd
start_api.bat
```

**Option C: PowerShell**
```powershell
.\start_api.ps1
```

### 3. Server Details
- **URL**: http://localhost:10000
- **Endpoint**: `/generate` (POST)
- **Content-Type**: application/json

## API Usage

### Request Format
```json
{
  "message": "How many years of experience do you have in Data Science?"
}
```

### Response Format
```json
{
  "response": {
    "content": "I have 3 years of experience in Data Science."
  }
}
```

## Supported Question Types

1. **Experience Questions**
   - Data Science, Machine Learning, AI, Deep Learning
   - Python, AWS, Tableau, Power BI, MySQL

2. **Salary Questions**
   - Current CTC: 5 LPA
   - Expected CTC: 8 LPA

3. **Technical Skills**
   - Programming languages, frameworks, tools

4. **Personal Information**
   - Location, Education, Notice Period

## Configuration

The server uses environment variables for personalization:
- `ADDRESS`: Current location
- `DEGREE`: Educational degree
- `MAJOR`: Field of study
- `UNIVERSITY`: University name

## Profile Information

**Default Profile**: Deepak Garg
- **Role**: Data Science & ML Developer
- **Experience**: 3 years total
- **Company**: Appsquadz
- **Education**: B.Tech Computer Science, 8.06 CGPA
- **Notice Period**: 15 days

## Model Information

- **Model**: openai-community/gpt2
- **Framework**: Transformers (Hugging Face)
- **Device**: Auto-detected (CPU/GPU)
- **Max Tokens**: 15 (for focused responses)

## Integration

This API server is automatically used by:
- `naukri_automation_original.py`
- `naukri_recommendations_original.py`
- `run_multiple_original.py`

Make sure the server is running before executing any automation scripts.

## Troubleshooting

1. **Model Loading Issues**: Ensure sufficient RAM/VRAM for GPT-2
2. **Port Conflicts**: Change port in `server.py` if 10000 is occupied
3. **Dependencies**: Install all packages from `requirements.txt`
4. **Path Issues**: Run from the API directory or update file paths
