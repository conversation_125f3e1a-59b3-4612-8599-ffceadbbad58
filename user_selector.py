"""
Multi-User Naukri Automation Interface
Allows selection between different users with their own credentials and data files
"""

import os
import shutil
from pathlib import Path
import sys

class UserManager:
    def __init__(self):
        self.users = {
            "1": {
                "name": "Deep<PERSON>",
                "env_file": ".env.deepak",
                "data_file": "data/mydata.txt",
                "excel_file": "job_applications_deepak.xlsx"
            },
            "2": {
                "name": "Nishka", 
                "env_file": ".env.nishka",
                "data_file": "data/data_nishka.txt",
                "excel_file": "job_applications_nishka.xlsx"
            }
        }
        self.current_user = None
        
    def display_users(self):
        """Display available users"""
        print("\n" + "="*50)
        print("🚀 NAUKRI AUTOMATION - USER SELECTION")
        print("="*50)
        print("\nAvailable Users:")
        for key, user in self.users.items():
            print(f"{key}. {user['name']}")
        print("\n0. Exit")
        print("-"*50)
        
    def select_user(self):
        """Allow user selection"""
        while True:
            self.display_users()
            choice = input("\nSelect user (0-2): ").strip()
            
            if choice == "0":
                print("👋 Goodbye!")
                sys.exit(0)
            elif choice in self.users:
                self.current_user = self.users[choice]
                print(f"\n✅ Selected user: {self.current_user['name']}")
                return True
            else:
                print("❌ Invalid choice. Please try again.")
                
    def setup_user_environment(self):
        """Setup environment for selected user"""
        if not self.current_user:
            print("❌ No user selected!")
            return False
            
        try:
            # Copy user-specific .env file to main .env
            env_source = self.current_user['env_file']
            if os.path.exists(env_source):
                shutil.copy2(env_source, '.env')
                print(f"✅ Loaded environment for {self.current_user['name']}")
            else:
                print(f"⚠️ Environment file {env_source} not found!")
                return False
                
            # Ensure data file exists
            data_file = self.current_user['data_file']
            if not os.path.exists(data_file):
                # Create directory if it doesn't exist
                os.makedirs(os.path.dirname(data_file), exist_ok=True)
                
                # Copy from template if available
                if os.path.exists("data/mydata.txt"):
                    shutil.copy2("data/mydata.txt", data_file)
                    print(f"✅ Created data file for {self.current_user['name']}")
                else:
                    # Create empty file
                    with open(data_file, 'w', encoding='utf-8') as f:
                        f.write("")
                    print(f"✅ Created empty data file for {self.current_user['name']}")
                    
            print(f"✅ Environment setup complete for {self.current_user['name']}")
            return True
            
        except Exception as e:
            print(f"❌ Error setting up environment: {str(e)}")
            return False
            
    def create_user_env_files(self):
        """Create user-specific environment files if they don't exist"""
        try:
            # Create .env.deepak if it doesn't exist
            if not os.path.exists('.env.deepak'):
                deepak_env = """# Naukri automation credentials - DEEPAK
NAUKRI_EMAIL=<EMAIL>
NAUKRI_PASSWORD=your_password_here

# Personal information
FIRST_NAME=Deepak   
LAST_NAME=Sharma  
PHONE=+91XXXXXXXXXX
ADDRESS=Your Address
HEADLINE=Your Headline
COVER_LETTER=null
RESUME_PATH=null

# Location details
CITY=your_city
POSTAL_CODE=000000
STATE=your_state

# Social profiles
GITHUB_URL=https://github.com/your_username/
LINKEDIN_URL=https://www.linkedin.com/in/your_profile/

# Education details
UNIVERSITY=Your University
DEGREE=Your Degree
MAJOR=Your Major

# Certification answers (Yes/No)
HAS_DBS=Yes
HAS_CRIMINAL_CONVICTIONS=No
HAS_VALID_CERT=Yes
"""
                with open('.env.deepak', 'w') as f:
                    f.write(deepak_env)
                print("✅ Created .env.deepak template")
                
            # Create .env.nishka from current .env
            if not os.path.exists('.env.nishka') and os.path.exists('.env'):
                shutil.copy2('.env', '.env.nishka')
                print("✅ Created .env.nishka from current .env")
                
        except Exception as e:
            print(f"❌ Error creating user environment files: {str(e)}")
            
    def show_current_config(self):
        """Show current user configuration"""
        if not self.current_user:
            print("❌ No user selected!")
            return
            
        print(f"\n📋 Current Configuration for {self.current_user['name']}:")
        print(f"   Environment: {self.current_user['env_file']}")
        print(f"   Data File: {self.current_user['data_file']}")
        print(f"   Excel File: {self.current_user['excel_file']}")
        
        # Show if files exist
        env_exists = "✅" if os.path.exists(self.current_user['env_file']) else "❌"
        data_exists = "✅" if os.path.exists(self.current_user['data_file']) else "❌"
        
        print(f"   Environment File: {env_exists}")
        print(f"   Data File: {data_exists}")
        
    def get_user_data_file(self):
        """Get the data file path for current user"""
        if self.current_user:
            return self.current_user['data_file']
        return "data/mydata.txt"  # Default fallback
        
    def get_user_excel_file(self):
        """Get the Excel file path for current user"""
        if self.current_user:
            return self.current_user['excel_file']
        return "job_applications.xlsx"  # Default fallback

def main():
    """Main interface for user selection"""
    user_manager = UserManager()
    
    # Create user environment files if they don't exist
    user_manager.create_user_env_files()
    
    # Select user
    if user_manager.select_user():
        # Setup environment
        if user_manager.setup_user_environment():
            user_manager.show_current_config()
            
            # Show script options
            print(f"\n🎯 Ready to run automation for {user_manager.current_user['name']}!")
            print("\nAvailable Scripts:")
            print("1. Main Automation (main.py)")
            print("2. Recommendations (naukri_recommendations_original.py)")
            print("3. Multiple Scripts (run_multiple_original.py)")
            print("4. Just setup environment (exit)")
            
            choice = input("\nSelect script to run (1-4): ").strip()
            
            if choice == "1":
                os.system("python main.py")
            elif choice == "2":
                os.system("python naukri_recommendations_original.py")
            elif choice == "3":
                os.system("python run_multiple_original.py")
            elif choice == "4":
                print("✅ Environment setup complete. You can now run any script.")
            else:
                print("❌ Invalid choice")
        else:
            print("❌ Failed to setup user environment")
    else:
        print("❌ User selection failed")

if __name__ == "__main__":
    main()
