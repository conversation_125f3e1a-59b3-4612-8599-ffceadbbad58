from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, StaleElementReferenceException, WebDriverException, NoSuchElementException
import time
import pandas as pd
import requests
import re
import os
import sys
import json
import threading
from dotenv import load_dotenv
from datetime import datetime
from user_config import get_excel_file, get_data_file, get_user_name

# Load environment variables
load_dotenv()

# Constants
MAX_RETRIES = 3
PAGE_LOAD_TIMEOUT = 30
APPLICATION_RETRY_ATTEMPTS = 1  # Number of retry attempts for failed applications
API_URL = "http://localhost:10000/generate"
# Store job applications data
applications = []
excel_lock = threading.Lock()  # For thread-safe Excel operations

# Coordination system
INSTANCE_ID = os.getenv('AUTOMATION_INSTANCE_ID', f'standalone_{int(time.time())}')
COORDINATION_ENABLED = os.getenv('AUTOMATION_COORDINATION_ENABLED', 'false').lower() == 'true'
STATE_FILE = os.getenv('AUTOMATION_STATE_FILE', 'automation_shared_state.json')

class CoordinationClient:
    """Client for coordinating with other automation instances"""
    
    def __init__(self):
        self.instance_id = INSTANCE_ID
        self.enabled = COORDINATION_ENABLED
        self.state_file = STATE_FILE
        
    def request_chatbot_lock(self):
        """Request exclusive lock for chatbot handling"""
        if not self.enabled:
            return True
            
        max_wait = 300  # 5 minutes max wait
        wait_time = 0
        
        while wait_time < max_wait:
            try:
                state = self._read_state()
                if not state.get("chatbot_in_progress", False):
                    state["chatbot_in_progress"] = True
                    state["chatbot_handler"] = self.instance_id
                    self._write_state(state)
                    print(f"🤖 {self.instance_id} acquired chatbot lock")
                    return True
            except:
                pass
            
            time.sleep(2)
            wait_time += 2
            if wait_time % 30 == 0:
                print(f"⏳ {self.instance_id} waiting for chatbot lock... ({wait_time}s)")
        
        print(f"⏰ {self.instance_id} timeout waiting for chatbot lock")
        return False
    
    def release_chatbot_lock(self):
        """Release chatbot lock"""
        if not self.enabled:
            return
            
        try:
            state = self._read_state()
            if state.get("chatbot_handler") == self.instance_id:
                state["chatbot_in_progress"] = False
                state["chatbot_handler"] = None
                self._write_state(state)
                print(f"🔓 {self.instance_id} released chatbot lock")
        except:
            pass
    
    def request_excel_lock(self):
        """Request exclusive lock for Excel file operations"""
        if not self.enabled:
            return True
            
        max_wait = 120  # 2 minutes max wait for Excel operations
        wait_time = 0
        
        while wait_time < max_wait:
            try:
                state = self._read_state()
                if not state.get("excel_in_progress", False):
                    state["excel_in_progress"] = True
                    state["excel_handler"] = self.instance_id
                    self._write_state(state)
                    print(f"📊 {self.instance_id} acquired Excel lock")
                    return True
            except:
                pass
            
            time.sleep(1)
            wait_time += 1
            if wait_time % 15 == 0:
                print(f"⏳ {self.instance_id} waiting for Excel lock... ({wait_time}s)")
        
        print(f"⏰ {self.instance_id} timeout waiting for Excel lock")
        return False
    
    def release_excel_lock(self):
        """Release Excel lock"""
        if not self.enabled:
            return
            
        try:
            state = self._read_state()
            if state.get("excel_handler") == self.instance_id:
                state["excel_in_progress"] = False
                state["excel_handler"] = None
                self._write_state(state)
                print(f"🔓 {self.instance_id} released Excel lock")
        except:
            pass
    
    def update_applications_count(self, count):
        """Update the total applications saved count"""
        if not self.enabled:
            return
            
        try:
            state = self._read_state()
            state["total_applications_saved"] = state.get("total_applications_saved", 0) + count
            self._write_state(state)
            print(f"📈 Total applications saved across all instances: {state['total_applications_saved']}")
        except:
            pass

    def get_radio_response(self, question_type):
        """Get predefined response for radio button questions"""
        if not self.enabled:
            return None
            
        try:
            state = self._read_state()
            responses = state.get("radio_button_responses", {})
            return responses.get(question_type.lower(), None)
        except:
            return None
    
    def _read_state(self):
        """Read shared state"""
        if not os.path.exists(self.state_file):
            return {}
        try:
            with open(self.state_file, 'r') as f:
                return json.load(f)
        except:
            return {}
    
    def _write_state(self, state):
        """Write shared state"""
        try:
            with open(self.state_file, 'w') as f:
                json.dump(state, f, indent=2)
        except:
            pass

# Initialize coordination client
coordination_client = CoordinationClient()

def get_blacklist_file():
    """Get user-specific blacklist file path"""
    user_name = get_user_name().lower()
    return f"data/blacklist_{user_name}.txt"

def load_permanent_blacklist():
    """Load permanently blacklisted companies from file"""
    blacklist_file = get_blacklist_file()
    blacklisted_companies = set()

    # Add manual blacklist entries
    manual_blacklist = [
        "Benovymed Healthcare Private Limited"
    ]
    blacklisted_companies.update(manual_blacklist)

    if os.path.exists(blacklist_file):
        try:
            with open(blacklist_file, 'r', encoding='utf-8') as f:
                for line in f:
                    company = line.strip()
                    if company:
                        blacklisted_companies.add(company)
        except Exception as e:
            print(f"⚠️ Error loading blacklist: {e}")

    return blacklisted_companies

def save_to_permanent_blacklist(company):
    """Save company to permanent blacklist file"""
    blacklist_file = get_blacklist_file()

    # Ensure data directory exists
    os.makedirs(os.path.dirname(blacklist_file), exist_ok=True)

    try:
        # Check if already in blacklist
        existing_blacklist = load_permanent_blacklist()
        if company not in existing_blacklist:
            with open(blacklist_file, 'a', encoding='utf-8') as f:
                f.write(f"{company}\n")
            print(f"🚫 Added to permanent blacklist: {company}")
        return True
    except Exception as e:
        print(f"⚠️ Error saving to blacklist: {e}")
        return False

def safe_append_to_excel(new_data, filename=None):
    """Thread-safe and process-safe function to append data to Excel file with enhanced blacklist logic"""
    if filename is None:
        filename = get_excel_file()
    
    # Request coordination lock for Excel operations
    if not coordination_client.request_excel_lock():
        print(f"⏰ {INSTANCE_ID} could not acquire Excel lock, using fallback save")
        fallback_filename = f"job_applications_backup_{INSTANCE_ID}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        pd.DataFrame(new_data).to_excel(fallback_filename, index=False)
        print(f"💾 Data saved to fallback file: {fallback_filename}")
        return
    
    try:
        with excel_lock:
            print(f"📊 {INSTANCE_ID} processing {len(new_data)} applications for Excel save")
            
            # Load permanent blacklist
            permanent_blacklist = load_permanent_blacklist()

            # Check if file exists
            if os.path.exists(filename):
                # Read existing data
                existing_df = pd.read_excel(filename)

                # Enhanced company analysis
                company_counts = existing_df['Company'].value_counts()

                # Companies with >4 applications get permanently blacklisted
                companies_to_blacklist = company_counts[company_counts > 4].index.tolist()
                for company in companies_to_blacklist:
                    if company not in permanent_blacklist:
                        save_to_permanent_blacklist(company)
                        permanent_blacklist.add(company)

                # Companies with >2 applications (but ≤4) get title-specific filtering
                frequent_companies = company_counts[company_counts > 2].index.tolist()

                # Load job title counts to prevent duplicates
                title_counts = existing_df.groupby(['Job Title', 'Company']).size()

                # Filter new data with enhanced logic
                filtered_data = []
                for record in new_data:
                    company = record.get('Company', 'Unknown')
                    job_title = record.get('Job Title', 'Unknown')

                    # Skip if company is permanently blacklisted
                    if company in permanent_blacklist:
                        print(f"🚫 Skipping {job_title} at {company} - Permanently blacklisted")
                        continue

                    # Skip if company has >2 applications and same job title applied >2 times
                    if company in frequent_companies:
                        if (job_title, company) in title_counts and title_counts[(job_title, company)] >= 2:
                            print(f"⚠️ Skipping {job_title} at {company} - Same title applied 2+ times at frequent company")
                            continue

                    # Skip if same job title at same company applied >2 times (general rule)
                    if (job_title, company) in title_counts and title_counts[(job_title, company)] >= 2:
                        print(f"⚠️ Skipping {job_title} at {company} - Already applied 2+ times")
                        continue

                    # Skip "Apply on company site" to prevent large files
                    if record.get('Status', '').lower() in ['external application required', 'company site']:
                        print(f"⚠️ Skipping {job_title} at {company} - External application")
                        continue

                    filtered_data.append(record)

                if filtered_data:
                    # Combine with existing data
                    new_df = pd.DataFrame(filtered_data)
                    combined_df = pd.concat([existing_df, new_df], ignore_index=True)
                else:
                    combined_df = existing_df
            else:
                # Create new file
                combined_df = pd.DataFrame(new_data)

            # Save to Excel
            combined_df.to_excel(filename, index=False)
            print(f"✅ {INSTANCE_ID} saved data to {filename}")
            
            # Update coordination stats
            coordination_client.update_applications_count(len(new_data))

    except Exception as e:
        print(f"❌ {INSTANCE_ID} error saving to Excel: {str(e)}")
        # Fallback: save with timestamp
        fallback_filename = f"job_applications_backup_{INSTANCE_ID}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        pd.DataFrame(new_data).to_excel(fallback_filename, index=False)
        print(f"💾 Data saved to backup file: {fallback_filename}")
    finally:
        # Always release the Excel lock
        coordination_client.release_excel_lock()

def extract_job_metadata(driver):
    """Extract comprehensive job metadata from job page"""
    metadata = {
        'Job Title': 'Unknown',
        'Company': 'Unknown',
        'Location': 'Unknown',
        'Experience': 'Unknown',
        'Salary': 'Unknown',
        'Job Description': 'Unknown',
        'Skills': 'Unknown',
        'Posted Date': 'Unknown'
    }

    try:
        # Job Title
        try:
            title_element = driver.find_element(By.CSS_SELECTOR, "h1.jd-header-title, h1[data-automation='job-title'], .jd-header-title")
            metadata['Job Title'] = title_element.text.strip()
        except:
            pass

        # Company Name
        try:
            company_element = driver.find_element(By.CSS_SELECTOR, ".jd-header-comp-name, [data-automation='company-name'], .comp-name")
            metadata['Company'] = company_element.text.strip()
        except:
            pass

        # Location
        try:
            location_element = driver.find_element(By.CSS_SELECTOR, ".jd-location, [data-automation='job-location'], .location")
            metadata['Location'] = location_element.text.strip()
        except:
            pass

        # Experience
        try:
            exp_element = driver.find_element(By.CSS_SELECTOR, ".jd-other-details .exp, .experience, [data-automation='experience']")
            metadata['Experience'] = exp_element.text.strip()
        except:
            pass

        # Salary
        try:
            salary_element = driver.find_element(By.CSS_SELECTOR, ".jd-other-details .salary, .salary, [data-automation='salary']")
            metadata['Salary'] = salary_element.text.strip()
        except:
            pass

        # Job Description (first 200 chars)
        try:
            desc_element = driver.find_element(By.CSS_SELECTOR, ".jd-desc, .job-description, [data-automation='job-description']")
            full_desc = desc_element.text.strip()
            metadata['Job Description'] = full_desc[:200] + "..." if len(full_desc) > 200 else full_desc
        except:
            pass

        # Skills/Tags
        try:
            skill_elements = driver.find_elements(By.CSS_SELECTOR, ".jd-skill-tag, .skill-tag, .tags li")
            skills = [skill.text.strip() for skill in skill_elements[:10]]  # Limit to 10 skills
            metadata['Skills'] = ", ".join(skills) if skills else 'Unknown'
        except:
            pass

        # Posted Date
        try:
            date_element = driver.find_element(By.CSS_SELECTOR, ".jd-stats .posted, .posted-date, [data-automation='posted-date']")
            metadata['Posted Date'] = date_element.text.strip()
        except:
            pass

    except Exception as e:
        print(f"Error extracting job metadata: {str(e)}")

    return metadata

def chat_with_bot(question):
    """Send chatbot question to API and get the response"""
    try:
        print(f"🔗 {INSTANCE_ID} Sending API request to {API_URL}")
        response = requests.post(API_URL, json={"message": question}, timeout=30)
        print(f"📡 {INSTANCE_ID} API response status: {response.status_code}")
        print(f"📡 {INSTANCE_ID} API response: {response.text}")
        if response.status_code == 200:
            return response.json()["response"]["content"]
        else:
            print(f"❌ {INSTANCE_ID} API returned non-200 status: {response.status_code}")
            return None
    except requests.exceptions.Timeout as e:
        print(f"⏰ {INSTANCE_ID} API request timeout: {e}")
        return None
    except requests.exceptions.ConnectionError as e:
        print(f"🔌 {INSTANCE_ID} API connection error: {e}")
        return None
    except requests.exceptions.RequestException as e:
        print(f"❌ {INSTANCE_ID} API request failed: {e}")
        return None
    except Exception as e:
        print(f"❌ {INSTANCE_ID} Unexpected error in chat_with_bot: {e}")
        return None
    
def extract_phone_number(driver):
    """Extract phone numbers from job description using regex"""
    try:
        jd_container = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "section.styles_job-desc-container__txpYf"))
        )
        jd_text = jd_container.text
        
        # Enhanced phone number regex pattern
        phone_regex = r'\b(?:\+?\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}\b|\b\d{10}\b|\b\d{5}[-.\s]?\d{5}\b'
        numbers_found = re.findall(phone_regex, jd_text)
        
        # Clean and validate numbers
        valid_numbers = []
        for num in numbers_found:
            # Remove non-digit characters
            cleaned_num = re.sub(r'\D', '', num)
            # Check for valid length (9-10 digits)
            if 9 <= len(cleaned_num) <= 10:
                # Format with last 10 digits if longer
                valid_numbers.append(cleaned_num[-10:] if len(cleaned_num) > 10 else cleaned_num)
        
        return ", ".join(valid_numbers) if valid_numbers else "Not Found"
        
    except Exception as e:
        print(f"Error extracting phone number: {str(e)}")
        return "Error"

def setup_driver():
    options = webdriver.ChromeOptions()
    options.add_argument("--start-maximized")
    options.add_argument("--disable-notifications")
    driver = webdriver.Chrome(options=options)
    driver.set_page_load_timeout(PAGE_LOAD_TIMEOUT)
    return driver, WebDriverWait(driver, 20)

def handle_authentication(driver, wait):
    try:
        driver.get("https://www.naukri.com/")
        login_button = wait.until(EC.element_to_be_clickable((By.ID, "login_Layer")))
        login_button.click()

        email_field = wait.until(EC.element_to_be_clickable((By.XPATH, "//input[@type='text']")))
        email_field.send_keys(os.getenv('NAUKRI_EMAIL'))
        
        password_field = driver.find_element(By.XPATH, "//input[@type='password']")
        password_field.send_keys(os.getenv('NAUKRI_PASSWORD'))
        password_field.send_keys(Keys.RETURN)

        wait.until(EC.url_contains("naukri.com"))
        print("Authentication successful")
    except Exception as e:
        print(f"Authentication failed: {str(e)}")
        raise

def configure_search(wait, search_terms=None, locations=None):
    try:
        # Default values if not provided
        if search_terms is None:
            search_terms = "Data analytics,research analyst,Data Analyst,Data Analysis,python developer, Machine Learning, Artificial Intelligence"
        if locations is None:
            locations = "Noida,Gurugram,Pune,Delhi"

        print(f"🔍 Searching for: {search_terms}")
        print(f"📍 Locations: {locations}")

        search_bar = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "div.nI-gNb-sb__main")))
        search_bar.click()

        search_input = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "input.suggestor-input")))
        search_input.clear()
        search_input.send_keys(search_terms)

        # Experience filter
        experience_input = wait.until(EC.presence_of_element_located((By.ID, "experienceDD")))
        experience_input.click()
        experience_options = wait.until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, "ul.dropdown li")))
        experience_options[3].click()

        # Location filter
        location_input = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "div.nI-gNb-sb__locations input.suggestor-input")))
        location_input.clear()
        location_input.send_keys(locations)

        # Execute search
        wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "button.nI-gNb-sb__icon-wrapper"))).click()
        print("Search configured successfully")
    except Exception as e:
        print(f"Search configuration failed: {str(e)}")
        raise

def apply_filters(wait):
    try:
        salary_ranges = ['3-6 Lakhs', '6-10 Lakhs', '10-15 Lakhs']
        for salary in salary_ranges:
            try:
                wait.until(EC.element_to_be_clickable((By.XPATH, f"//span[@title='{salary}']"))).click()
                print(f"Applied salary filter: {salary}")
                time.sleep(1)
            except:
                print(f"Failed to apply {salary} filter")

        # Education filter
        wait.until(EC.element_to_be_clickable((By.ID, "educationViewMoreId"))).click()
        time.sleep(8)
        wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, ".styles_filter-apply-btn__MDAUd"))).click()
        print("Education filters applied")
    except Exception as e:
        print(f"Filter application failed: {str(e)}")
        raise
        
    try:
        # Freshness
        print("Applying freshness filter")
        freshness_button = wait.until(EC.element_to_be_clickable((By.ID, "filter-freshness")))
        freshness_button.click()
        freshness_options = wait.until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, "ul[data-filter-id='freshness'] li")))
        freshness_options[1].click()  # Select 'Last 15 days'
    except Exception as e:
        print(f"Failed to apply freshness filter: {str(e)}")


def get_new_window(driver, original_handles):
    """Wait for and return new window handle"""
    try:
        WebDriverWait(driver, 10).until(
            lambda d: len(d.window_handles) > len(original_handles)
        )
        return [h for h in driver.window_handles if h not in original_handles][0]
    except TimeoutException:
        print("New window did not open in time")
        return None

def handle_chatbot(driver):
    """Handle chatbot interaction with coordination and multiple safety checks"""
    status = "Application Failed"
    chatbot_used = "No"
    max_interactions = 5
    interactions = 0
    start_time = time.time()
    max_chatbot_time = 300  # 5 minutes maximum for entire chatbot interaction

    # Request chatbot lock for coordination
    if not coordination_client.request_chatbot_lock():
        print(f"⏰ {INSTANCE_ID} could not acquire chatbot lock, skipping")
        return status, chatbot_used

    try:
        WebDriverWait(driver, 15).until(
            EC.presence_of_element_located((By.CLASS_NAME, "chatbot_MessageContainer"))
        )
        chatbot_used = "Yes"
        print(f"🤖 {INSTANCE_ID} chatbot detected - starting conversation")

        while interactions < max_interactions:
            # Check for overall timeout
            if time.time() - start_time > max_chatbot_time:
                print(f"⏰ {INSTANCE_ID} Chatbot interaction timeout ({max_chatbot_time}s), ending")
                break

            try:
                # Check for success message first
                success_elements = driver.find_elements(
                    By.XPATH, '//div[contains(@class, "apply-status-header") and contains(@class, "green")]'
                )
                if success_elements and "successfully applied" in success_elements[0].text.lower():
                    status = "Successfully applied via chatbot"
                    break

                # Get all bot messages
                bot_messages = driver.find_elements(By.CSS_SELECTOR, 'div.botMsg.msg div span')
                if not bot_messages:
                    print(f"🤖 {INSTANCE_ID} No bot messages found, ending conversation")
                    break

                last_question = bot_messages[-1].text.strip()
                if not last_question:
                    print(f"🤖 {INSTANCE_ID} Empty question, ending conversation")
                    break

                print(f"Chatbot question ({interactions+1}/{max_interactions}): {last_question}")

                # Handle radio buttons with save button
                radio_handled = False
                try:
                        # Check for radio button container first
                        radio_container = WebDriverWait(driver, 5).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, ".singleselect-radiobutton"))
                        )
                        
                        # Try radio buttons first with explicit wait for clickability
                        radio_buttons = WebDriverWait(driver, 5).until(
                            EC.presence_of_all_elements_located((By.CSS_SELECTOR, ".ssrc__radio-btn-container input[type='radio']"))
                        )
                        if radio_buttons:
                            print(f"🎯 {INSTANCE_ID} found {len(radio_buttons)} radio button options")
                            
                            # Select appropriate radio button based on question context and coordination
                            selected_radio = None
                            question_lower = last_question.lower()
                            
                            # Try to get coordinated response first
                            coordinated_response = None
                            if "notice period" in question_lower:
                                coordinated_response = coordination_client.get_radio_response("notice_period")
                            elif "ctc" in question_lower and "current" in question_lower:
                                coordinated_response = coordination_client.get_radio_response("current_ctc")
                            elif "ctc" in question_lower and "expected" in question_lower:
                                coordinated_response = coordination_client.get_radio_response("expected_ctc")
                            
                            # Smart selection based on question type and coordination
                            if coordinated_response:
                                for radio in radio_buttons:
                                    value = radio.get_attribute('value')
                                    if value and coordinated_response in value:
                                        selected_radio = radio
                                        print(f"🎯 {INSTANCE_ID} using coordinated response: {coordinated_response}")
                                        break
                            
                            if not selected_radio and "notice period" in question_lower:
                                # Try to select "1 Month" or "2 Months" for notice period
                                for radio in radio_buttons:
                                    value = radio.get_attribute('value')
                                    if value and ("1 Month" in value or "2 Months" in value):
                                        selected_radio = radio
                                        break
                            
                            # If no smart selection made, select first available radio button
                            if not selected_radio:
                                selected_radio = WebDriverWait(driver, 5).until(
                                    EC.element_to_be_clickable((By.CSS_SELECTOR, ".ssrc__radio-btn-container input[type='radio']:not([disabled])"))
                                )
                            
                            # Click the selected radio button
                            driver.execute_script("arguments[0].click();", selected_radio)
                            selected_value = selected_radio.get_attribute('value')
                            print(f"✅ {INSTANCE_ID} selected radio button: {selected_value}")

                            # Look for save/submit button after radio selection
                            try:
                                # Wait a bit for any dynamic updates
                                time.sleep(1)
                                
                                # Try multiple button selectors
                                save_selectors = [
                                    "button[type='submit']",
                                    "button.savesrc__button",
                                    "button[class*='save']",
                                    "button[class*='submit']",
                                    ".chatbot_SendMessage button",
                                    "button.sendMsg"
                                ]
                                
                                save_button = None
                                for selector in save_selectors:
                                    try:
                                        save_button = WebDriverWait(driver, 3).until(
                                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                                        )
                                        break
                                    except:
                                        continue
                                
                                if save_button:
                                    driver.execute_script("arguments[0].click();", save_button)
                                    print("Clicked save/submit button")
                                    radio_handled = True
                                    time.sleep(3)  # Wait for next question
                                else:
                                    print("No save button found, radio selection might auto-submit")
                                    radio_handled = True
                                    time.sleep(3)
                                    
                            except Exception as e:
                                print(f"No save button found after radio selection: {e}")
                                # Sometimes radio selection auto-submits
                                radio_handled = True
                                time.sleep(3)

                except (TimeoutException, NoSuchElementException):
                    print("No radio buttons found, will try text input")

                if not radio_handled:
                    # Fallback to text input
                    try:
                        input_field = WebDriverWait(driver, 5).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, "#userInput__f9ndj8bujInputBox, div[contenteditable='true'].textArea"))
                        )
                        print(f"🤖 {INSTANCE_ID} Calling API for question: {last_question}")
                        answer = chat_with_bot(last_question)
                        if not answer:
                            print(f"❌ {INSTANCE_ID} Empty response from chatbot API, skipping")
                            break

                        input_field.clear()
                        input_field.send_keys(answer)
                        print(f"✅ {INSTANCE_ID} Provided text input: {answer}")

                        # Click send button
                        send_button = WebDriverWait(driver, 5).until(
                            EC.element_to_be_clickable((By.CLASS_NAME, "sendMsg"))
                        )
                        send_button.click()
                        print(f"📤 {INSTANCE_ID} Sent chatbot response")

                    except (TimeoutException, NoSuchElementException) as e:
                        print(f"❌ {INSTANCE_ID} No recognizable input method found: {e}")
                        break
                    except Exception as e:
                        print(f"❌ {INSTANCE_ID} Text input failed: {e}")
                        break

                interactions += 1
                time.sleep(2)  # Allow chatbot processing

            except Exception as e:
                print(f"Chatbot interaction error: {str(e)}")
                interactions += 1
                break

    except TimeoutException:
        print(f"⏰ {INSTANCE_ID} no chatbot detected within timeout")
    except Exception as e:
        print(f"❌ {INSTANCE_ID} chatbot handling failed: {str(e)}")
    finally:
        # Always release the chatbot lock
        coordination_client.release_chatbot_lock()

    return status, chatbot_used

def comprehensive_success_check(driver, chatbot_used="No"):
    """
    Comprehensive and robust success detection with multiple verification methods
    Returns: (status, is_successful)
    """
    print(f"🔍 {INSTANCE_ID} performing comprehensive success verification...")
    
    # Wait for page to stabilize after application
    time.sleep(3)
    
    # Multiple success indicators to check
    success_indicators = [
        # Primary success messages
        ('xpath', '//div[contains(@class, "apply-status-header") and contains(@class, "green")]'),
        ('xpath', '//div[contains(text(), "successfully applied") or contains(text(), "Successfully Applied")]'),
        ('xpath', '//div[contains(text(), "Application submitted") or contains(text(), "Application Submitted")]'),
        
        # Alternative success patterns
        ('css', '.success-message, .apply-success, .application-success'),
        ('css', '[class*="success"][class*="apply"], [class*="applied"][class*="success"]'),
        
        # Thank you messages
        ('xpath', '//div[contains(text(), "Thank you") and contains(text(), "application")]'),
        ('xpath', '//div[contains(text(), "Application received") or contains(text(), "Application Received")]'),
        
        # Confirmation messages
        ('css', '.confirmation-message, .apply-confirmation'),
        ('xpath', '//div[contains(@class, "confirmation") and contains(text(), "appli")]'),
    ]
    
    # Check for success indicators
    for selector_type, selector in success_indicators:
        try:
            if selector_type == 'xpath':
                elements = driver.find_elements(By.XPATH, selector)
            else:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
            
            for element in elements:
                element_text = element.text.lower()
                success_keywords = [
                    'successfully applied', 'application submitted', 'application received',
                    'thank you for applying', 'applied successfully', 'application confirmed',
                    'application sent', 'we have received your application'
                ]
                
                if any(keyword in element_text for keyword in success_keywords):
                    status = f"Successfully applied via {'chatbot' if chatbot_used == 'Yes' else 'direct'}"
                    print(f"✅ {INSTANCE_ID} SUCCESS DETECTED: {element_text[:100]}...")
                    return status, True
                    
        except Exception as e:
            continue
    
    # No clear success detected
    print(f"❓ {INSTANCE_ID} No clear success indication found")
    return "Application Status Unclear", False

def apply_with_retry(driver, job_url, job_metadata, current_page, max_retries=1):
    """
    Apply to job with retry mechanism if application fails
    Returns: (record, job_processed_successfully)
    """
    print(f"🎯 {INSTANCE_ID} Starting application process with retry mechanism")
    
    for attempt in range(max_retries + 1):
        if attempt > 0:
            print(f"🔄 {INSTANCE_ID} RETRY ATTEMPT {attempt}/{max_retries}")
            # Refresh page for retry
            driver.refresh()
            time.sleep(3)
        
        try:
            status = "Application Failed"
            chatbot_used = "No"
            
            # Check for already applied status first
            try:
                already_applied = WebDriverWait(driver, 5).until(
                    EC.visibility_of_element_located((By.ID, "already-applied"))
                )
                return {
                    "Job Link": job_url,
                    "Status": "Already Applied",
                    "Chatbot Used": "No",
                    "Phone Number": "Not Checked",
                    "Page": current_page,
                    "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "Retry Attempt": attempt + 1,
                    **job_metadata
                }, True
            except TimeoutException:
                pass

            # Check for company site redirect
            try:
                company_site_btn = WebDriverWait(driver, 3).until(
                    EC.visibility_of_element_located((By.ID, "company-site-button"))
                )
                return {
                    "Job Link": job_url,
                    "Status": "External Application Required",
                    "Chatbot Used": "No",
                    "Phone Number": "Not Checked",
                    "Page": current_page,
                    "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "Retry Attempt": attempt + 1,
                    **job_metadata
                }, True
            except TimeoutException:
                pass

            # Click apply button
            try:
                apply_btn = WebDriverWait(driver, 15).until(
                    EC.element_to_be_clickable((By.ID, "apply-button"))
                )
                apply_btn.click()
                print(f"🖱️ {INSTANCE_ID} Clicked apply button (Attempt {attempt + 1})")
                time.sleep(3)
            except TimeoutException:
                if attempt < max_retries:
                    continue
                status = "Apply button not found"
                break

            # Handle chatbot interaction
            status, chatbot_used = handle_chatbot(driver)
            
            # Comprehensive success verification
            final_status, is_successful = comprehensive_success_check(driver, chatbot_used)
            
            if is_successful:
                print(f"🎉 {INSTANCE_ID} APPLICATION SUCCESSFUL on attempt {attempt + 1}")
                return {
                    "Job Link": job_url,
                    "Status": final_status,
                    "Chatbot Used": chatbot_used,
                    "Phone Number": "Not Checked",
                    "Page": current_page,
                    "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "Retry Attempt": attempt + 1,
                    **job_metadata
                }, True
            else:
                if attempt < max_retries:
                    print(f"🔄 {INSTANCE_ID} Will retry application...")
                    continue
                status = final_status
                break
                    
        except Exception as e:
            if attempt < max_retries:
                continue
            status = f"Application Error: {str(e)}"
            break
    
    # All retries exhausted
    print(f"💔 {INSTANCE_ID} Application failed after {max_retries + 1} attempts")
    return {
        "Job Link": job_url,
        "Status": status,
        "Chatbot Used": chatbot_used,
        "Phone Number": "Not Checked",
        "Page": current_page,
        "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "Retry Attempt": max_retries + 1,
        **job_metadata
    }, False

def process_job_page(driver, wait, current_page):

    try:
        # Initial job list retrieval with retries
        jobs = wait.until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, "div.srp-jobtuple-wrapper")))
        print(f"\nFound {len(jobs)} jobs on page {current_page}")

        for idx in range(1, len(jobs)+1):
            job_processed = False
            for retry in range(MAX_RETRIES + 1):
                try:
                    if retry > 0:
                        print(f"Retry {retry}/{MAX_RETRIES} for job {idx}")
                        jobs = wait.until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, "div.srp-jobtuple-wrapper")))
                        if idx-1 >= len(jobs):
                            print(f"Job index {idx} out of bounds after refresh, skipping")
                            break
                        job = jobs[idx-1]

                    # Refresh job reference each iteration
                    jobs = wait.until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, "div.srp-jobtuple-wrapper")))
                    job = jobs[idx-1] if idx-1 < len(jobs) else None
                    if not job:
                        print(f"Job index {idx} no longer exists, skipping")
                        break

                    link = WebDriverWait(job, 10).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "a.title")))
                    job_url = link.get_attribute('href').strip()
                    if not job_url:
                        raise ValueError("Empty job URL")

                    print(f"\nProcessing job {idx}/{len(jobs)}: {job_url[:60]}...")

                    # Window management
                    original_windows = driver.window_handles
                    driver.execute_script("window.open(arguments[0]);", job_url)
                    new_window = get_new_window(driver, original_windows)

                    if not new_window:
                        raise WebDriverException("Failed to open new window")

                    driver.switch_to.window(new_window)
                    status = "Application Failed"
                    chatbot_used = "No"
                    phone_number = "Not Checked"

                    try:
                        # Extract job metadata first
                        job_metadata = extract_job_metadata(driver)
                        print(f"📋 Job: {job_metadata['Job Title']} at {job_metadata['Company']}")

                        # Use new retry mechanism for application
                        record, application_success = apply_with_retry(
                            driver, job_url, job_metadata, current_page, max_retries=APPLICATION_RETRY_ATTEMPTS
                        )
                        
                        applications.append(record)
                        
                        if application_success:
                            print(f"✅ {INSTANCE_ID} Job {idx} processed successfully")
                        else:
                            print(f"❌ {INSTANCE_ID} Job {idx} failed after retries")

                    except Exception as e:
                        print(f"❌ {INSTANCE_ID} Critical error processing job {idx}: {str(e)}")
                        # Create minimal record for failed job
                        failed_record = {
                            "Job Link": job_url if 'job_url' in locals() else "Unknown",
                            "Status": f"Critical Error: {str(e)}",
                            "Chatbot Used": "No",
                            "Phone Number": "Not Checked",
                            "Page": current_page,
                            "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                            "Retry Attempt": 1,
                            "Job Title": "Unknown",
                            "Company": "Unknown",
                            "Location": "Unknown",
                            "Experience": "Unknown",
                            "Salary": "Unknown",
                            "Job Description": "Unknown",
                            "Skills": "Unknown",
                            "Posted Date": "Unknown"
                        }
                        applications.append(failed_record)

                    # Cleanup windows
                    try:
                        driver.close()
                        driver.switch_to.window(original_windows[0])
                    except Exception as e:
                        print(f"⚠️ {INSTANCE_ID} Window cleanup error: {e}")
                    
                    job_processed = True
                    break

                except StaleElementReferenceException:
                    print(f"🔄 {INSTANCE_ID} Stale element reference while processing job {idx}")
                    time.sleep(2)
                except Exception as e:
                    print(f"❌ {INSTANCE_ID} Error processing job {idx}: {str(e)}")
                    if retry == MAX_RETRIES:
                        print(f"💔 {INSTANCE_ID} Max retries reached for job {idx}")
                    time.sleep(1)

            if not job_processed:
                print(f"💔 {INSTANCE_ID} Failed to process job {idx} after {MAX_RETRIES} retries")
                # Create minimal record for failed job
                failed_record = {
                    "Job Link": job_url if 'job_url' in locals() else "Unknown",
                    "Status": "Failed after retries",
                    "Chatbot Used": "No",
                    "Phone Number": "Not Checked",
                    "Page": current_page,
                    "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "Retry Attempt": MAX_RETRIES,
                    "Job Title": "Unknown",
                    "Company": "Unknown",
                    "Location": "Unknown",
                    "Experience": "Unknown",
                    "Salary": "Unknown",
                    "Job Description": "Unknown",
                    "Skills": "Unknown",
                    "Posted Date": "Unknown"
                }
                applications.append(failed_record)

        return True

    except Exception as e:
        print(f"💥 {INSTANCE_ID} Critical error processing page {current_page}: {str(e)}")
        return False

def comprehensive_success_check(driver, chatbot_used="No"):
    """
    Comprehensive and robust success detection with multiple verification methods
    Returns: (status, is_successful)
    """
    print(f"🔍 {INSTANCE_ID} performing comprehensive success verification...")
    
    # Wait for page to stabilize after application
    time.sleep(3)
    
    # Multiple success indicators to check
    success_indicators = [
        # Primary success messages
        ('xpath', '//div[contains(@class, "apply-status-header") and contains(@class, "green")]'),
        ('xpath', '//div[contains(text(), "successfully applied") or contains(text(), "Successfully Applied")]'),
        ('xpath', '//div[contains(text(), "Application submitted") or contains(text(), "Application Submitted")]'),
        
        # Alternative success patterns
        ('css', '.success-message, .apply-success, .application-success'),
        ('css', '[class*="success"][class*="apply"], [class*="applied"][class*="success"]'),
        
        # Thank you messages
        ('xpath', '//div[contains(text(), "Thank you") and contains(text(), "application")]'),
        ('xpath', '//div[contains(text(), "Application received") or contains(text(), "Application Received")]'),
        
        # Confirmation messages
        ('css', '.confirmation-message, .apply-confirmation'),
        ('xpath', '//div[contains(@class, "confirmation") and contains(text(), "appli")]'),
    ]
    
    # Check for success indicators
    for selector_type, selector in success_indicators:
        try:
            if selector_type == 'xpath':
                elements = driver.find_elements(By.XPATH, selector)
            else:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
            
            for element in elements:
                element_text = element.text.lower()
                success_keywords = [
                    'successfully applied', 'application submitted', 'application received',
                    'thank you for applying', 'applied successfully', 'application confirmed',
                    'application sent', 'we have received your application'
                ]
                
                if any(keyword in element_text for keyword in success_keywords):
                    status = f"Successfully applied via {'chatbot' if chatbot_used == 'Yes' else 'direct'}"
                    print(f"✅ {INSTANCE_ID} SUCCESS DETECTED: {element_text[:100]}...")
                    return status, True
                    
        except Exception as e:
            continue
    
    # Check for "Already Applied" status (also considered successful)
    try:
        already_applied_selectors = [
            (By.ID, "already-applied"),
            (By.XPATH, '//div[contains(text(), "already applied") or contains(text(), "Already Applied")]'),
            (By.CSS_SELECTOR, '.already-applied, [class*="already"][class*="applied"]')
        ]
        
        for by_type, selector in already_applied_selectors:
            elements = driver.find_elements(by_type, selector)
            if elements and any(elem.is_displayed() for elem in elements):
                print(f"✅ {INSTANCE_ID} ALREADY APPLIED detected")
                return "Already Applied", True
                
    except Exception as e:
        pass
    
    # Check for failure indicators
    failure_indicators = [
        ('xpath', '//div[contains(text(), "failed") or contains(text(), "error") or contains(text(), "unable")]'),
        ('css', '.error-message, .apply-error, .application-error'),
        ('xpath', '//div[contains(@class, "error") and contains(text(), "appli")]'),
    ]
    
    for selector_type, selector in failure_indicators:
        try:
            if selector_type == 'xpath':
                elements = driver.find_elements(By.XPATH, selector)
            else:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
            
            for element in elements:
                if element.is_displayed():
                    error_text = element.text[:100]
                    print(f"❌ {INSTANCE_ID} FAILURE DETECTED: {error_text}...")
                    return f"Application Failed: {error_text}", False
                    
        except Exception as e:
            continue
    
    # Check if still on application form (indicates incomplete application)
    try:
        apply_button = driver.find_elements(By.ID, "apply-button")
        chatbot_containers = driver.find_elements(By.CLASS_NAME, "chatbot_MessageContainer")
        
        if apply_button and apply_button[0].is_displayed():
            print(f"⚠️ {INSTANCE_ID} Still on application form - application incomplete")
            return "Application Incomplete - Still on form", False
        
        if chatbot_containers and chatbot_containers[0].is_displayed():
            # Check if chatbot is still waiting for input
            input_fields = driver.find_elements(By.CSS_SELECTOR, 
                'div[contenteditable="true"].textArea, input[type="text"]')
            radio_buttons = driver.find_elements(By.CSS_SELECTOR, 
                '.ssrc__radio-btn-container input[type="radio"]')
            
            if input_fields or radio_buttons:
                print(f"⚠️ {INSTANCE_ID} Chatbot still waiting for input - application incomplete")
                return "Application Incomplete - Chatbot pending", False
                
    except Exception as e:
        pass
    
    # If no clear success or failure found, check page URL and title
    try:
        current_url = driver.current_url.lower()
        page_title = driver.title.lower()
        
        if 'success' in current_url or 'applied' in current_url:
            print(f"✅ {INSTANCE_ID} SUCCESS indicated by URL: {current_url}")
            return "Successfully applied - URL indicates success", True
        
        if 'success' in page_title or 'applied' in page_title:
            print(f"✅ {INSTANCE_ID} SUCCESS indicated by page title: {page_title}")
            return "Successfully applied - Title indicates success", True
            
    except Exception as e:
        pass
    
    # Final check - look for any positive confirmation text on page
    try:
        page_text = driver.find_element(By.TAG_NAME, "body").text.lower()
        positive_phrases = [
            'application submitted', 'successfully applied', 'application received',
            'thank you for applying', 'we will contact you', 'application confirmed'
        ]
        
        for phrase in positive_phrases:
            if phrase in page_text:
                print(f"✅ {INSTANCE_ID} SUCCESS detected in page text")
                return f"Successfully applied - {phrase} found", True
                
    except Exception as e:
        pass
    
    # No clear success detected
    print(f"❓ {INSTANCE_ID} No clear success indication found")
    return "Application Status Unclear", False

def apply_with_retry(driver, job_url, job_metadata, current_page, max_retries=1):
    """
    Apply to job with retry mechanism if application fails
    Returns: (record, job_processed_successfully)
    """
    print(f"🎯 {INSTANCE_ID} Starting application process with retry mechanism")
    
    for attempt in range(max_retries + 1):
        if attempt > 0:
            print(f"🔄 {INSTANCE_ID} RETRY ATTEMPT {attempt}/{max_retries}")
            # Refresh page for retry
            driver.refresh()
            time.sleep(3)
            
            # Wait for page to load
            try:
                WebDriverWait(driver, 15).until(
                    lambda d: d.execute_script("return document.readyState") == "complete"
                )
            except TimeoutException:
                print(f"⏰ {INSTANCE_ID} Page reload timeout on retry {attempt}")
                continue
        
        try:
            status = "Application Failed"
            chatbot_used = "No"
            phone_number = "Not Checked"
            
            print(f"📋 {INSTANCE_ID} Job: {job_metadata['Job Title']} at {job_metadata['Company']} (Attempt {attempt + 1})")

            # Check for already applied status first
            try:
                already_applied = WebDriverWait(driver, 5).until(
                    EC.visibility_of_element_located((By.ID, "already-applied"))
                )
                status = "Already Applied"
                print(f"✅ {INSTANCE_ID} Already applied detected")
                
                # Still extract phone if possible
                try:
                    phone_number = extract_phone_number(driver)
                    print(f"📞 {INSTANCE_ID} Found phone numbers: {phone_number}")
                except Exception as e:
                    print(f"📞 {INSTANCE_ID} Phone extraction failed: {str(e)}")
                    phone_number = "Error"
                
                # Create record for already applied
                record = {
                    "Job Link": job_url,
                    "Status": status,
                    "Chatbot Used": chatbot_used,
                    "Phone Number": phone_number,
                    "Page": current_page,
                    "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "Retry Attempt": attempt + 1,
                    **job_metadata
                }
                return record, True
                
            except TimeoutException:
                pass

            # Check for company site redirect
            try:
                company_site_btn = WebDriverWait(driver, 3).until(
                    EC.visibility_of_element_located((By.ID, "company-site-button"))
                )
                status = "External Application Required"
                print(f"🔗 {INSTANCE_ID} External application required")
                
                record = {
                    "Job Link": job_url,
                    "Status": status,
                    "Chatbot Used": chatbot_used,
                    "Phone Number": "Not Checked",
                    "Page": current_page,
                    "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "Retry Attempt": attempt + 1,
                    **job_metadata
                }
                return record, True
                
            except TimeoutException:
                pass

            # Click apply button
            try:
                apply_btn = WebDriverWait(driver, 15).until(
                    EC.element_to_be_clickable((By.ID, "apply-button"))
                )
                apply_btn.click()
                print(f"🖱️ {INSTANCE_ID} Clicked apply button (Attempt {attempt + 1})")
                time.sleep(3)  # Wait for application process to start
                
            except TimeoutException:
                print(f"❌ {INSTANCE_ID} Apply button not found (Attempt {attempt + 1})")
                if attempt < max_retries:
                    continue
                else:
                    status = "Apply button not found"
                    break

            # Handle chatbot interaction
            status, chatbot_used = handle_chatbot(driver)
            
            # Comprehensive success verification
            final_status, is_successful = comprehensive_success_check(driver, chatbot_used)
            
            if is_successful:
                print(f"🎉 {INSTANCE_ID} APPLICATION SUCCESSFUL on attempt {attempt + 1}")
                record = {
                    "Job Link": job_url,
                    "Status": final_status,
                    "Chatbot Used": chatbot_used,
                    "Phone Number": "Not Checked",
                    "Page": current_page,
                    "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "Retry Attempt": attempt + 1,
                    **job_metadata
                }
                return record, True
            else:
                print(f"❌ {INSTANCE_ID} APPLICATION FAILED on attempt {attempt + 1}: {final_status}")
                if attempt < max_retries:
                    print(f"🔄 {INSTANCE_ID} Will retry application...")
                    time.sleep(2)
                    continue
                else:
                    status = final_status
                    break
                    
        except Exception as e:
            print(f"❌ {INSTANCE_ID} Application error on attempt {attempt + 1}: {str(e)}")
            if attempt < max_retries:
                print(f"🔄 {INSTANCE_ID} Will retry due to error...")
                time.sleep(2)
                continue
            else:
                status = f"Application Error: {str(e)}"
                break
    
    # All retries exhausted
    print(f"💔 {INSTANCE_ID} Application failed after {max_retries + 1} attempts")
    record = {
        "Job Link": job_url,
        "Status": status,
        "Chatbot Used": chatbot_used,
        "Phone Number": "Not Checked",
        "Page": current_page,
        "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "Retry Attempt": max_retries + 1,
        **job_metadata
    }
    return record, False

def main():
    # Parse command line arguments
    search_terms = None
    locations = None

    if len(sys.argv) > 1:
        search_terms = sys.argv[1]
        print(f"📝 Using custom search terms: {search_terms}")

    if len(sys.argv) > 2:
        locations = sys.argv[2]
        print(f"📍 Using custom locations: {locations}")

    print(f"🚀 Starting Naukri Automation for {get_user_name()}")
    print(f"📊 Data will be saved to: {get_excel_file()}")
    print("="*60)

    try:
        driver, wait = setup_driver()
        handle_authentication(driver, wait)
        configure_search(wait, search_terms, locations)
        apply_filters(wait)

        current_page = 1
        while True:
            print(f"\n{'='*40}\nProcessing page {current_page}\n{'='*40}")
            if not process_job_page(driver, wait, current_page):
                print(f"Stopped processing at page {current_page}")
                break

            # Enhanced pagination handling
            try:
                next_btn = wait.until(EC.presence_of_element_located(
                    (By.XPATH, "//a[contains(., 'Next') and not(contains(@class, 'disabled'))]"))
                )
                if next_btn.is_enabled():
                    next_btn.click()
                    print(f"Navigated to page {current_page + 1}")
                    current_page += 1
                    # Wait for page stability
                    WebDriverWait(driver, PAGE_LOAD_TIMEOUT).until(
                        lambda d: d.execute_script("return document.readyState") == "complete"
                    )
                    time.sleep(2)  # Additional stabilization time
                else:
                    print("Next button is disabled")
                    break
            except TimeoutException:
                print("No more pages available")
                break

    except Exception as e:
        print(f"Main process error: {str(e)}")
    finally:
        # Ensure data preservation using robust Excel handling
        try:
            if applications:
                safe_append_to_excel(applications)
                print(f"\n✅ {len(applications)} job applications processed and saved")
            else:
                print("\n⚠️ No applications to save")
        except Exception as e:
            print(f"Error saving data: {str(e)}")

        if 'driver' in locals():
            driver.quit()
            print("Browser closed")

if __name__ == "__main__":
    main()
