from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, StaleElementReferenceException, WebDriverException, NoSuchElementException
import time
import pandas as pd
import requests
import os
from dotenv import load_dotenv
from datetime import datetime
import threading
from user_config import get_excel_file, get_data_file, get_user_name

# Load environment variables
load_dotenv()

# Comprehensive error suppression
import warnings
import sys
from contextlib import redirect_stderr
import io

warnings.filterwarnings("ignore")
os.environ['PYTHONWARNINGS'] = 'ignore'

# Suppress all stderr output to hide stacktraces
class NullWriter:
    def write(self, txt): pass
    def flush(self): pass

sys.stderr = NullWriter()

# Constants
MAX_RETRIES = 3
PAGE_LOAD_TIMEOUT = 30
API_URL = "http://localhost:10000/generate"
# Store job applications data
applications = []
excel_lock = threading.Lock()  # For thread-safe Excel operations

def get_blacklist_file():
    """Get user-specific blacklist file path"""
    user_name = get_user_name().lower()
    return f"data/blacklist_{user_name}.txt"

def load_permanent_blacklist():
    """Load permanently blacklisted companies from file"""
    blacklist_file = get_blacklist_file()
    blacklisted_companies = set()

    # Add manual blacklist entries
    manual_blacklist = [
        "Benovymed Healthcare Private Limited"
    ]
    blacklisted_companies.update(manual_blacklist)

    if os.path.exists(blacklist_file):
        try:
            with open(blacklist_file, 'r', encoding='utf-8') as f:
                for line in f:
                    company = line.strip()
                    if company:
                        blacklisted_companies.add(company)
        except Exception as e:
            print(f"⚠️ Error loading blacklist: {e}")

    return blacklisted_companies

def save_to_permanent_blacklist(company):
    """Save company to permanent blacklist file"""
    blacklist_file = get_blacklist_file()

    # Ensure data directory exists
    os.makedirs(os.path.dirname(blacklist_file), exist_ok=True)

    try:
        # Check if already in blacklist
        existing_blacklist = load_permanent_blacklist()
        if company not in existing_blacklist:
            with open(blacklist_file, 'a', encoding='utf-8') as f:
                f.write(f"{company}\n")
            print(f"🚫 Added to permanent blacklist: {company}")
        return True
    except Exception as e:
        print(f"⚠️ Error saving to blacklist: {e}")
        return False

def safe_append_to_excel(new_data, filename=None):
    """Thread-safe function to append data to Excel file with enhanced blacklist logic"""
    if filename is None:
        filename = get_excel_file()
    with excel_lock:
        try:
            # Load permanent blacklist
            permanent_blacklist = load_permanent_blacklist()

            # Check if file exists
            if os.path.exists(filename):
                # Read existing data
                existing_df = pd.read_excel(filename)

                # Enhanced company analysis
                company_counts = existing_df['Company'].value_counts()

                # Companies with >4 applications get permanently blacklisted
                companies_to_blacklist = company_counts[company_counts > 4].index.tolist()
                for company in companies_to_blacklist:
                    if company not in permanent_blacklist:
                        save_to_permanent_blacklist(company)
                        permanent_blacklist.add(company)

                # Companies with >2 applications (but ≤4) get title-specific filtering
                frequent_companies = company_counts[company_counts > 2].index.tolist()

                # Load job title counts to prevent duplicates
                title_counts = existing_df.groupby(['Job Title', 'Company']).size()

                # Filter new data with enhanced logic
                filtered_data = []
                for record in new_data:
                    company = record.get('Company', 'Unknown')
                    job_title = record.get('Job Title', 'Unknown')

                    # Skip if company is permanently blacklisted
                    if company in permanent_blacklist:
                        print(f"🚫 Skipping {job_title} at {company} - Permanently blacklisted")
                        continue

                    # Skip if company has >2 applications and same job title applied >2 times
                    if company in frequent_companies:
                        if (job_title, company) in title_counts and title_counts[(job_title, company)] >= 2:
                            print(f"⚠️ Skipping {job_title} at {company} - Same title applied 2+ times at frequent company")
                            continue

                    # Skip if same job title at same company applied >2 times (general rule)
                    if (job_title, company) in title_counts and title_counts[(job_title, company)] >= 2:
                        print(f"⚠️ Skipping {job_title} at {company} - Already applied 2+ times")
                        continue

                    # Skip "Apply on company site" to prevent large files
                    if record.get('Status', '').lower() in ['external application required', 'company site']:
                        print(f"⚠️ Skipping {job_title} at {company} - External application")
                        continue

                    filtered_data.append(record)

                if filtered_data:
                    # Combine with existing data
                    new_df = pd.DataFrame(filtered_data)
                    combined_df = pd.concat([existing_df, new_df], ignore_index=True)
                else:
                    combined_df = existing_df
            else:
                # Create new file
                combined_df = pd.DataFrame(new_data)

            # Save to Excel
            combined_df.to_excel(filename, index=False)
            print(f"✅ Data appended to {filename}")

        except Exception as e:
            print(f"❌ Error saving to Excel: {str(e)}")
            # Fallback: save with timestamp
            fallback_filename = f"job_applications_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            pd.DataFrame(new_data).to_excel(fallback_filename, index=False)
            print(f"💾 Data saved to backup file: {fallback_filename}")

def extract_job_metadata_from_card(job_element):
    """Extract job metadata from recommendation job card on listing page"""
    metadata = {
        'Job Title': 'Unknown',
        'Company': 'Unknown',
        'Location': 'Unknown',
        'Experience': 'Unknown',
        'Salary': 'Unknown',
        'Job Description': 'Unknown',
        'Skills': 'Unknown',
        'Posted Date': 'Unknown'
    }

    try:
        # Job Title - from the card structure
        try:
            title_element = job_element.find_element(By.CSS_SELECTOR, "p.title.ellipsis.typ-16Bold")
            metadata['Job Title'] = title_element.get_attribute("title") or title_element.text.strip()
            print(f"✅ Found job title: {metadata['Job Title']}")
        except Exception as e:
            pass  # Silently continue - will try detail page extraction

        # Company Name - from the card structure
        try:
            company_element = job_element.find_element(By.CSS_SELECTOR, ".companyWrapper .subTitle.ellipsis")
            metadata['Company'] = company_element.get_attribute("title") or company_element.text.strip()
            print(f"✅ Found company: {metadata['Company']}")
        except Exception as e:
            pass  # Silently continue - will try detail page extraction

        # Experience - from the card structure
        try:
            exp_element = job_element.find_element(By.CSS_SELECTOR, ".placeHolderLi.experience .ellipsis")
            metadata['Experience'] = exp_element.get_attribute("title") or exp_element.text.strip()
            print(f"✅ Found experience: {metadata['Experience']}")
        except Exception as e:
            pass  # Silently continue - will try detail page extraction

        # Salary - from the card structure
        try:
            salary_element = job_element.find_element(By.CSS_SELECTOR, ".placeHolderLi.salary .ellipsis")
            metadata['Salary'] = salary_element.get_attribute("title") or salary_element.text.strip()
            print(f"✅ Found salary: {metadata['Salary']}")
        except Exception as e:
            pass  # Silently continue - will try detail page extraction

        # Location - from the card structure
        try:
            location_element = job_element.find_element(By.CSS_SELECTOR, ".placeHolderLi.location .ellipsis")
            metadata['Location'] = location_element.get_attribute("title") or location_element.text.strip()
            print(f"✅ Found location: {metadata['Location']}")
        except Exception as e:
            pass  # Silently continue - will try detail page extraction

        # Job Description - from the card structure
        try:
            desc_element = job_element.find_element(By.CSS_SELECTOR, ".job-description span")
            metadata['Job Description'] = desc_element.get_attribute("title") or desc_element.text.strip()
            print(f"✅ Found job description: {metadata['Job Description'][:100]}...")
        except Exception as e:
            pass  # Silently continue - will try detail page extraction

        # Skills - from the card structure
        try:
            skill_elements = job_element.find_elements(By.CSS_SELECTOR, ".tags li.dot")
            skills = [skill.text.strip() for skill in skill_elements if skill.text.strip()]
            metadata['Skills'] = ", ".join(skills) if skills else "Unknown"
            print(f"✅ Found skills: {metadata['Skills']}")
        except Exception as e:
            pass  # Silently continue - will try detail page extraction

        # Posted Date - from the card structure
        try:
            posted_element = job_element.find_element(By.CSS_SELECTOR, ".jobTupleFooter .fw500")
            metadata['Posted Date'] = posted_element.text.strip()
            print(f"✅ Found posted date: {metadata['Posted Date']}")
        except Exception as e:
            # Silently skip posted date extraction
            pass

    except Exception as e:
        print(f"❌ Error extracting job metadata: {str(e)}")

    return metadata

def safe_extract_element(driver, selectors, element_name, timeout=3):
    """Safely extract element text using multiple selectors with no error output"""
    import io
    from contextlib import redirect_stderr

    for selector in selectors:
        try:
            # Suppress all stderr output including Selenium stacktraces
            with redirect_stderr(io.StringIO()):
                element = WebDriverWait(driver, timeout).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                )
                text = element.text.strip()
                if text:
                    # Only print for important fields, suppress for posted date and other minor fields
                    if element_name.lower() not in ['posted date', 'job posted date', 'posted']:
                        print(f"✅ Found {element_name}: {text}")
                    return text
        except Exception:
            # Silently continue to next selector - no error output
            continue

    # Don't print error message - just return Unknown silently
    return 'Unknown'

def extract_job_metadata_recommendations(driver):
    """Extract job metadata from recommendation job detail page"""
    metadata = {
        'Job Title': 'Unknown',
        'Company': 'Unknown',
        'Location': 'Unknown',
        'Experience': 'Unknown',
        'Salary': 'Unknown',
        'Job Description': 'Unknown',
        'Skills': 'Unknown',
        'Posted Date': 'Unknown'
    }

    try:
        # Wait for page to load
        WebDriverWait(driver, 10).until(
            lambda d: d.execute_script("return document.readyState") == "complete"
        )
        time.sleep(2)  # Additional wait for dynamic content

        # Job Title - using safe extraction
        title_selectors = [
            "h1.styles_jd-header-title__rZwM1",  # Your exact structure
            "h1[class*='jd-header-title']",       # Partial class match
            "h1[class*='header-title']",          # More generic
            ".jd-header-title",                   # Generic fallback
            "h1",                                 # Last resort
            "[data-testid='job-title']"           # Test ID fallback
        ]
        try:
            metadata['Job Title'] = safe_extract_element(driver, title_selectors, "job title")
        except:
            pass

        try:
            # Company Name - using safe extraction
            company_selectors = [
                "a.styles_jhc__hiring-for__NG9SF",    # Your exact structure
                ".styles_jd-header-comp-name__MvqAI a", # Parent container
                "a[class*='hiring-for']",              # Partial class match
                ".company-name a",                     # Generic fallback
                ".jd-header-comp-name a",              # Another fallback
                "a[href*='company']",                  # Link-based fallback
                "[data-testid='company-name']"         # Test ID fallback
            ]
            metadata['Company'] = safe_extract_element(driver, company_selectors, "company")
        except:
            pass

        try:
            # Experience - using safe extraction
            exp_selectors = [
                ".styles_jhc__exp__k_giM span",        # Your exact structure
                ".styles_jhc__exp-salary-container__NXsVd .styles_jhc__exp__k_giM span",
                ".exp span",                           # Generic fallback
                ".experience span",                    # Another fallback
                "[class*='exp'] span"                  # Partial class match
            ]
            metadata['Experience'] = safe_extract_element(driver, exp_selectors, "experience")
        except:
            pass

        try:
            # Salary - using safe extraction
            salary_selectors = [
                ".styles_jhc__salary__jdfEC span",     # Your exact structure
                ".styles_jhc__exp-salary-container__NXsVd .styles_jhc__salary__jdfEC span",
                ".salary span",                        # Generic fallback
                ".jhc__salary span",                   # Another fallback
                "[class*='salary'] span"               # Partial class match
            ]
            metadata['Salary'] = safe_extract_element(driver, salary_selectors, "salary")
        except:
            pass

        try:
            # Location - using safe extraction
            location_selectors = [
                ".styles_jhc__location__W_pVs a",      # Your exact structure
                ".styles_jhc__loc___Du2H span a",      # Parent container
                ".location a",                         # Generic fallback
                ".jhc__location a",                    # Another fallback
                "[class*='location'] a"                # Partial class match
            ]
            metadata['Location'] = safe_extract_element(driver, location_selectors, "location")
        except:
            pass

        try:
            # Posted Date - using safe extraction
            posted_selectors = [
                ".styles_jhc__stat__PgY67:first-child span:last-child", # Your exact structure
                ".styles_jhc__jd-stats__KrId0 .styles_jhc__stat__PgY67 span:last-child",
                ".posted-date",                        # Generic fallback
                ".jd-stats span",                      # Another fallback
                "[class*='stat'] span"                 # Partial class match
            ]
            metadata['Posted Date'] = safe_extract_element(driver, posted_selectors, "posted date")
        except:
            pass

        try:
            # Job Description - using safe extraction
            desc_selectors = [
                ".styles_JDC__dang-inner-html__h0K4t",  # Main job description container
                ".job-description",                      # Generic fallback
                ".jd-description",                       # Another fallback
                "[class*='description']"                 # Partial class match
            ]
            metadata['Job Description'] = safe_extract_element(driver, desc_selectors, "job description")
        except:
            pass

        try:
            # Skills - using safe extraction
            skill_selectors = [
                ".styles_jhc__skills__mVPAb .chip-container .chip", # Skills chips
                ".skills .chip",                         # Generic fallback
                ".skill-tags .tag",                      # Another fallback
                "[class*='skill'] [class*='chip']"       # Partial class match
            ]
            try:
                skill_elements = driver.find_elements(By.CSS_SELECTOR, skill_selectors[0])
                if skill_elements:
                    skills = [skill.text.strip() for skill in skill_elements if skill.text.strip()]
                    metadata['Skills'] = ", ".join(skills) if skills else "Unknown"
                    print(f"✅ Found skills: {metadata['Skills']}")
            except:
                metadata['Skills'] = "Unknown"
        except:
            pass

    except Exception:
        # Silently handle any remaining errors
        pass

    return metadata



def chat_with_bot(question):
    """Send chatbot question to API and get the response"""
    try:
        response = requests.post(API_URL, json={"message": question}, timeout=120)
        print(f"API response: {response.text}")
        if response.status_code == 200:
            return response.json()["response"]["content"]
    except requests.exceptions.RequestException:
        return None

def setup_driver():
    print("🔧 DEBUG: Setting up Chrome options...")
    options = webdriver.ChromeOptions()
    options.add_argument("--start-maximized")
    options.add_argument("--disable-notifications")
    print("🔧 DEBUG: Creating Chrome driver...")
    driver = webdriver.Chrome(options=options)
    print("🔧 DEBUG: Setting page load timeout...")
    driver.set_page_load_timeout(PAGE_LOAD_TIMEOUT)
    print("🔧 DEBUG: Driver setup complete!")
    return driver, WebDriverWait(driver, 20)

def handle_authentication(driver, wait):
    try:
        driver.get("https://www.naukri.com/")
        login_button = wait.until(EC.element_to_be_clickable((By.ID, "login_Layer")))
        login_button.click()

        email_field = wait.until(EC.element_to_be_clickable((By.XPATH, "//input[@type='text']")))
        email_field.send_keys(os.getenv('NAUKRI_EMAIL'))
        
        password_field = driver.find_element(By.XPATH, "//input[@type='password']")
        password_field.send_keys(os.getenv('NAUKRI_PASSWORD'))
        password_field.send_keys(Keys.RETURN)

        wait.until(EC.url_contains("naukri.com"))
        print("Authentication successful")
    except Exception as e:
        print(f"Authentication failed: {str(e)}")
        raise

def navigate_to_recommendations(driver, wait):
    """Navigate to recommendations page by clicking Jobs button"""
    try:
        print("Looking for Jobs button in navigation...")

        # Try multiple selectors for the Jobs button
        jobs_button_selectors = [
            "a[href='/mnjuser/recommendedjobs']",
            "a.nI-gNb-menuItems__anchorDropdown[title='Recommended Jobs']",
            "a[title='Recommended Jobs']",
            "//a[contains(@href, 'recommendedjobs')]",
            "//a[contains(text(), 'Jobs')]"
        ]

        jobs_button = None
        for selector in jobs_button_selectors:
            try:
                if selector.startswith("//"):
                    jobs_button = wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                else:
                    jobs_button = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, selector)))
                print(f"Found Jobs button using selector: {selector}")
                break
            except:
                continue

        if jobs_button:
            jobs_button.click()
            print("✅ Clicked on Jobs button")
            time.sleep(5)  # Wait for page to load

            # Wait for recommendations page to load
            try:
                wait.until(EC.presence_of_element_located((By.ID, "reco-header")))
                print("✅ Recommendations page loaded successfully")
            except:
                print("⚠️ Recommendations header not found, but continuing...")
        else:
            raise Exception("Jobs button not found with any selector")

    except Exception as e:
        print(f"Failed to click Jobs button: {str(e)}")
        print("Trying direct URL navigation as fallback...")
        driver.get("https://www.naukri.com/mnjuser/recommendedjobs")
        time.sleep(5)

def handle_chatbot_recommendations(driver):
    """Handle chatbot interaction - comprehensive fix"""
    status = "Application Failed"
    chatbot_used = "No"
    max_interactions = 15
    interactions = 0

    try:
        # Wait for chatbot to appear with multiple selectors
        chatbot_selectors = [
            ".chatbot_MessageContainer",
            "#chatbot-container",
            "[class*='chatbot']",
            ".chat-container"
        ]

        chatbot_found = False
        for selector in chatbot_selectors:
            try:
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                )
                chatbot_found = True
                break
            except:
                continue

        if not chatbot_found:
            print("No chatbot detected")
            return status, chatbot_used

        chatbot_used = "Yes"
        print("✅ Chatbot detected - starting conversation")

        while interactions < max_interactions:
            interactions += 1
            print(f"\n--- Chatbot Interaction {interactions}/{max_interactions} ---")

            try:
                # Check for success indicators first
                success_indicators = [
                    "successfully applied",
                    "application submitted",
                    "thank you for applying",
                    "application received"
                ]

                page_text = driver.page_source.lower()
                for indicator in success_indicators:
                    if indicator in page_text:
                        status = "Application Successful"
                        print(f"✅ Success detected: {indicator}")
                        return status, chatbot_used

                # Look for the latest bot question
                bot_question_selectors = [
                    "div.botMsg.msg div span",
                    ".chatbot_message:last-child",
                    ".bot-message:last-child"
                ]

                last_question = ""
                for selector in bot_question_selectors:
                    try:
                        bot_messages = driver.find_elements(By.CSS_SELECTOR, selector)
                        if bot_messages:
                            last_question = bot_messages[-1].text.strip()
                            if last_question:
                                break
                    except:
                        continue

                if not last_question:
                    print("No question found - ending conversation")
                    break

                print(f"Question: {last_question}")

                # Handle radio buttons first (most common)
                radio_buttons = driver.find_elements(By.CSS_SELECTOR, ".ssrc__radio-btn-container input[type='radio']")
                if radio_buttons and len(radio_buttons) > 0:
                    print(f"Found {len(radio_buttons)} radio options")

                    # Select first radio button
                    first_radio = radio_buttons[0]
                    option_text = first_radio.get_attribute('value') or first_radio.get_attribute('id')

                    # Click using JavaScript to ensure it works
                    driver.execute_script("arguments[0].click();", first_radio)
                    driver.execute_script("arguments[0].checked = true;", first_radio)
                    print(f"✅ Selected: {option_text}")

                    time.sleep(2)  # Wait for UI to update

                    # Look for save/submit button
                    save_selectors = [
                        "#sendMsg__e9ee2ilurInputBox .sendMsg",
                        ".sendMsg",
                        "button[type='submit']",
                        ".save-button",
                        "[class*='save']",
                        "[class*='submit']",
                        ".apply-button"
                    ]

                    button_clicked = False
                    for selector in save_selectors:
                        try:
                            save_btn = driver.find_element(By.CSS_SELECTOR, selector)
                            if save_btn.is_displayed() and save_btn.is_enabled():
                                driver.execute_script("arguments[0].click();", save_btn)
                                print(f"✅ Clicked save button: {selector}")
                                button_clicked = True
                                break
                        except:
                            continue

                    if not button_clicked:
                        # Try pressing Enter on the radio button
                        try:
                            first_radio.send_keys(Keys.RETURN)
                            print("✅ Pressed Enter on radio button")
                        except:
                            print("⚠️ Could not submit radio selection")

                    time.sleep(3)  # Wait for next question
                    continue

                # Handle text input (fallback)
                else:
                    print("Looking for text input field...")
                    input_selectors = [
                        "#userInput__f9ndj8bujInputBox",
                        "div[contenteditable='true'].textArea",
                        "div[contenteditable='true']",
                        "textarea",
                        "input[type='text']"
                    ]

                    input_field = None
                    for selector in input_selectors:
                        try:
                            input_field = driver.find_element(By.CSS_SELECTOR, selector)
                            if input_field.is_displayed():
                                print(f"✅ Found input field: {selector}")
                                break
                        except:
                            continue

                    if input_field:
                        # Get API response
                        answer = chat_with_bot(last_question)
                        if answer:
                            print(f"API Response: {answer}")

                            # Clear and focus
                            input_field.click()
                            if input_field.get_attribute("contenteditable") == "true":
                                driver.execute_script("arguments[0].innerHTML = '';", input_field)
                            else:
                                input_field.clear()

                            # Type character by character like human
                            for char in answer:
                                input_field.send_keys(char)
                                time.sleep(0.01)

                            print(f"✅ Typed answer: {answer}")

                            # Submit the answer
                            send_selectors = [
                                "#sendMsg__e9ee2ilurInputBox .sendMsg",
                                ".sendMsg",
                                "button[type='submit']"
                            ]

                            submitted = False
                            for selector in send_selectors:
                                try:
                                    send_btn = driver.find_element(By.CSS_SELECTOR, selector)
                                    if send_btn.is_displayed():
                                        driver.execute_script("arguments[0].click();", send_btn)
                                        print(f"✅ Clicked send: {selector}")
                                        submitted = True
                                        break
                                except:
                                    continue

                            if not submitted:
                                # Try Enter key
                                try:
                                    input_field.send_keys(Keys.RETURN)
                                    print("✅ Pressed Enter")
                                except:
                                    print("⚠️ Could not submit answer")

                            time.sleep(3)
                        else:
                            print("❌ No API response received")
                            break
                    else:
                        print("❌ No input method found")
                        break

            except Exception as e:
                print(f"❌ Error in interaction: {str(e)}")
                break

        # Final success check
        time.sleep(2)
        page_text = driver.page_source.lower()
        success_indicators = [
            "successfully applied",
            "application submitted",
            "thank you for applying",
            "application received"
        ]

        for indicator in success_indicators:
            if indicator in page_text:
                status = "Application Successful"
                print(f"✅ Final success check: {indicator}")
                break
        else:
            if interactions > 0:
                status = "Application Submitted"
                print("✅ Chatbot interaction completed")

    except Exception as e:
        print(f"❌ Chatbot error: {str(e)}")

    return status, chatbot_used


def process_recommendations_page(driver, wait, applications):
    """Process all jobs on the recommendations page"""
    try:
        # Get recommendation jobs using the correct selector based on your HTML structure
        jobs = wait.until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, "article.jobTuple")))

        if not jobs:
            print("⚠️ No recommended jobs found")
            return False

        print(f"\n📋 Processing {len(jobs)} recommended jobs...")

        for idx in range(len(jobs)):  # Process all available jobs
            print(f"\n🔍 Processing recommended job {idx+1}...")

            try:
                # Store original window handles
                original_windows = driver.window_handles

                # Get current job element (refresh to avoid stale references)
                try:
                    current_jobs = driver.find_elements(By.CSS_SELECTOR, "article.jobTuple")
                    if idx >= len(current_jobs):
                        print(f"⚠️ Job {idx+1} not found, skipping...")
                        continue
                    job_element = current_jobs[idx]
                except:
                    print(f"❌ Could not find job element {idx+1}")
                    continue

                # For recommendation page, we need to construct the job URL from data-job-id
                job_url = ""
                job_id = ""

                try:
                    # Get job ID from the article element
                    job_id = job_element.get_attribute("data-job-id")
                    if job_id:
                        # Construct job detail URL using job ID
                        job_url = f"https://www.naukri.com/job-listings-{job_id}"
                        print(f"📋 Constructed Job URL from ID {job_id}: {job_url}")
                    else:
                        print(f"❌ No data-job-id found for job {idx+1}")
                        continue

                except Exception as e:
                    print(f"❌ Error getting job ID for job {idx+1}: {str(e)}")
                    continue

                if not job_url:
                    print(f"❌ Could not construct job URL for job {idx+1}")
                    continue

                # Open job in new tab using direct URL navigation
                try:
                    print(f"🔗 Opening job {idx+1} in new tab...")
                    driver.execute_script("window.open(arguments[0]);", job_url)
                    time.sleep(3)

                    # Wait for new tab to open
                    WebDriverWait(driver, 10).until(lambda d: len(d.window_handles) > len(original_windows))
                    new_windows = driver.window_handles
                    new_window = [h for h in new_windows if h not in original_windows][0]
                    driver.switch_to.window(new_window)
                    print(f"✅ Opened job {idx+1} in new tab")

                except Exception as e:
                    print(f"❌ Failed to open job {idx+1}: {str(e)}")
                    continue

                status = "Application Failed"
                chatbot_used = "No"

                try:
                    # Extract job metadata from the card first (before opening job detail)
                    job_metadata = extract_job_metadata_from_card(job_element)
                    print(f"📋 Recommendation: {job_metadata['Job Title']} at {job_metadata['Company']}")

                    # Now extract additional metadata from job detail page
                    detail_metadata = extract_job_metadata_recommendations(driver)

                    # Merge metadata, preferring detail page data when available (but only if not 'Unknown')
                    # This way we keep card data if detail extraction fails
                    for key, value in detail_metadata.items():
                        if value != 'Unknown':
                            job_metadata[key] = value

                    # Check for already applied status
                    try:
                        WebDriverWait(driver, 5).until(
                            EC.visibility_of_element_located((By.ID, "already-applied"))
                        )
                        status = "Already Applied"
                        print(f"Already applied to recommended job {idx+1}")
                    except TimeoutException:
                        pass

                    # Check for company site redirect
                    try:
                        WebDriverWait(driver, 3).until(
                            EC.visibility_of_element_located((By.ID, "company-site-button"))
                        )
                        status = "External Application Required"
                        print(f"External application required for recommended job {idx+1}")
                    except TimeoutException:
                        pass

                    # If not already applied and not external, try to apply
                    if status == "Application Failed":
                        try:
                            # Look for apply button using the exact structure from your HTML
                            apply_button_selectors = [
                                "button#apply-button.styles_apply-button__uJI3A.apply-button",  # Your exact structure
                                "button#apply-button",                                          # ID only
                                "button.apply-button",                                         # Class only
                                "button[id='apply-button']",                                   # Attribute selector
                                ".styles_apply-button__uJI3A"                                 # Class selector
                            ]

                            apply_button = None
                            for selector in apply_button_selectors:
                                try:
                                    apply_button = WebDriverWait(driver, 5).until(
                                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                                    )
                                    print(f"✅ Found apply button with selector: {selector}")
                                    break
                                except:
                                    continue

                            if apply_button:
                                # Verify button is actually clickable and visible
                                if not (apply_button.is_displayed() and apply_button.is_enabled()):
                                    print("❌ Apply button found but not clickable")
                                    status = "Apply button not clickable"
                                else:
                                    print("✅ Apply button is clickable - attempting click...")

                                    # Try multiple click methods
                                    click_successful = False
                                    try:
                                        # Method 1: Regular click
                                        apply_button.click()
                                        click_successful = True
                                        print("✅ Apply button clicked successfully")
                                    except Exception as e:
                                        print(f"Regular click failed: {str(e)}")
                                        try:
                                            # Method 2: JavaScript click
                                            driver.execute_script("arguments[0].click();", apply_button)
                                            click_successful = True
                                            print("✅ Apply button clicked via JavaScript")
                                        except Exception as e2:
                                            print(f"JavaScript click failed: {str(e2)}")

                                    if not click_successful:
                                        status = "Apply button click failed"
                                        print("❌ Could not click apply button with any method")
                                    else:
                                        time.sleep(5)  # Wait for page to respond after successful click

                                # Handle chatbot if present (using the complete logic from main script)
                                try:
                                    chatbot_result = handle_chatbot_recommendations(driver)
                                    if chatbot_result and len(chatbot_result) == 2:
                                        status, chatbot_used = chatbot_result
                                    else:
                                        status = "Application Failed"
                                        chatbot_used = "No"
                                        print("⚠️ Chatbot function returned invalid result")
                                except Exception as e:
                                    status = "Application Failed"
                                    chatbot_used = "No"
                                    print(f"⚠️ Chatbot handling failed: {str(e)}")

                                # Additional success checking after chatbot interaction
                                if status == "Application Failed":
                                    try:
                                        # Check for various success indicators
                                        success_selectors = [
                                            ".apply-status-header.green",
                                            "[class*='success']",
                                            ".success-message",
                                            "[class*='applied']",
                                            ".application-success"
                                        ]

                                        for selector in success_selectors:
                                            try:
                                                success_element = WebDriverWait(driver, 2).until(
                                                    EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                                                )
                                                success_text = success_element.text.lower()
                                                if any(word in success_text for word in ["success", "applied", "submitted"]):
                                                    status = "Successfully applied"
                                                    print(f"✅ Success detected: {success_text}")
                                                    break
                                            except:
                                                continue
                                    except:
                                        pass
                            else:
                                print("❌ Apply button not found with any selector")
                                status = "Apply button not available"

                        except Exception as e:
                            print(f"Error during application: {str(e)}")
                            status = f"Application Error: {str(e)}"

                    # Final success check
                    try:
                        success_msg = WebDriverWait(driver, 10).until(
                            EC.visibility_of_element_located((By.XPATH,
                                '//div[contains(@class, "apply-status-header") and contains(@class, "green")]'))
                        )
                        if "successfully applied" in success_msg.text.lower():
                            status = "Successfully applied directly"
                    except TimeoutException:
                        pass

                    # Record application status with comprehensive metadata
                    record = {
                        "Job Link": job_url,
                        "Status": status,
                        "Chatbot Used": chatbot_used,
                        "Phone Number": "Not Checked",  # Default value
                        "Page": "Recommendations",
                        "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        **job_metadata  # Include all job metadata
                    }
                    applications.append(record)

                    # Cleanup windows
                    driver.close()
                    driver.switch_to.window(original_windows[0])
                    print(f"✅ Completed processing recommended job {idx+1}")

                except Exception as inner_e:
                    print(f"❌ Error in job processing: {str(inner_e)}")
                    # Create minimal record for failed job processing
                    failed_record = {
                        "Job Link": job_url if 'job_url' in locals() else "Unknown",
                        "Status": "Processing Error",
                        "Chatbot Used": "No",
                        "Phone Number": "Not Checked",
                        "Page": "Recommendations",
                        "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "Job Title": "Unknown",
                        "Company": "Unknown",
                        "Location": "Unknown",
                        "Experience": "Unknown",
                        "Salary": "Unknown",
                        "Job Description": "Unknown",
                        "Skills": "Unknown",
                        "Posted Date": "Unknown"
                    }
                    applications.append(failed_record)

            except Exception as e:
                print(f"❌ Error processing recommended job {idx+1}: {str(e)}")
                # Create minimal record for failed job
                failed_record = {
                    "Job Link": job_url if 'job_url' in locals() else "Unknown",
                    "Status": "Processing Error",
                    "Chatbot Used": "No",
                    "Phone Number": "Not Checked",
                    "Page": "Recommendations",
                    "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "Job Title": "Unknown",
                    "Company": "Unknown",
                    "Location": "Unknown",
                    "Experience": "Unknown",
                    "Salary": "Unknown",
                    "Job Description": "Unknown",
                    "Skills": "Unknown",
                    "Posted Date": "Unknown"
                }
                applications.append(failed_record)

                # Cleanup any open windows
                try:
                    current_windows = driver.window_handles
                    if len(current_windows) > 1:
                        driver.close()
                        driver.switch_to.window(current_windows[0])
                except:
                    pass

        return True

    except Exception as e:
        print(f"Critical error processing recommendations: {str(e)}")
        return False

def main():
    print("🔧 DEBUG: Main function started")
    print(f"🚀 Starting Naukri Recommendations Automation for {get_user_name()}")
    print("💡 Processing recommended jobs...")
    print(f"📊 Data will be saved to: {get_excel_file()}")
    print("="*60)

    try:
        print("🔧 DEBUG: About to setup driver")
        driver, wait = setup_driver()
        print("🔧 DEBUG: Driver setup completed")
        handle_authentication(driver, wait)
        navigate_to_recommendations(driver, wait)

        if process_recommendations_page(driver, wait, applications):
            print("\n✅ Recommendations processing completed!")
        else:
            print("\n❌ Recommendations processing failed!")

    except Exception as e:
        print(f"Main process error: {str(e)}")
    finally:
        # Ensure data preservation using robust Excel handling
        try:
            if applications:
                safe_append_to_excel(applications)
                print(f"\n✅ {len(applications)} recommendation applications processed and saved")
            else:
                print("\n⚠️ No recommendations processed")
        except Exception as e:
            print(f"Error saving data: {str(e)}")

        if 'driver' in locals():
            driver.quit()
            print("Browser closed")

if __name__ == "__main__":
    print("🔧 DEBUG: Script starting...")
    main()
