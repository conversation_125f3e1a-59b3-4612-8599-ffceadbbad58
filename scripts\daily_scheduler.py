import schedule
import time
import subprocess
import sys
import os
from datetime import datetime

def run_naukri_automation():
    """Run the naukri automation script"""
    print(f"🚀 Starting Naukri automation at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Change to script directory
        script_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(script_dir)
        
        # Run the automation script with timeout
        result = subprocess.run([sys.executable, "run_multiple.py"], 
                              capture_output=True, text=True, timeout=3600)  # 1 hour timeout
        
        if result.returncode == 0:
            print("✅ Automation completed successfully!")
            if result.stdout.strip():
                print(f"Output: {result.stdout}")
        else:
            print("❌ Automation failed!")
            print(f"Error: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print("⏰ Automation timed out after 1 hour")
    except Exception as e:
        print(f"💥 Error running automation: {e}")
    
    print(f"🏁 Automation finished at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 50)

# Schedule the job to run daily at 8:40 AM
schedule.every().day.at("08:40").do(run_naukri_automation)

print("🕐 Naukri Daily Scheduler Started")
print(f"📅 Current time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
print("⏰ Scheduled to run daily at 8:40 AM")
print("🛑 Press Ctrl+C to stop the scheduler")
print("-" * 50)

# Keep the script running
try:
    while True:
        schedule.run_pending()
        time.sleep(60)  # Check every minute
except KeyboardInterrupt:
    print("\n🛑 Scheduler stopped by user")
