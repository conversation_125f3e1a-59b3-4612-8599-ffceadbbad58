#!/usr/bin/env python3
"""
Test script for enhanced real-time blacklist synchronization
"""

import os
import sys
import time
import json
from datetime import datetime

# Enable coordination for the test
os.environ['AUTOMATION_COORDINATION_ENABLED'] = 'true'
os.environ['AUTOMATION_INSTANCE_ID'] = 'test_realtime_sync'

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from naukri_automation_original import CoordinationClient

def test_real_time_sync():
    print("🔄 TESTING REAL-TIME BLACKLIST SYNCHRONIZATION")
    print("="*60)
    
    # Initialize coordination client
    client = CoordinationClient()
    
    print("1. Testing enhanced company tracking with metadata...")
    
    # Simulate job metadata from different companies
    test_jobs = [
        {
            "Company": "TCS",
            "Job Title": "Software Engineer",
            "Location": "Mumbai",
            "Salary": "5-8 LPA",
            "Experience": "2-4 years"
        },
        {
            "Company": "TCS", 
            "Job Title": "Data Analyst",
            "Location": "Pune",
            "Salary": "4-7 LPA",
            "Experience": "1-3 years"
        },
        {
            "Company": "Infosys",
            "Job Title": "Python Developer", 
            "Location": "Bangalore",
            "Salary": "6-10 LPA",
            "Experience": "3-5 years"
        },
        {
            "Company": "TCS",  # This should trigger blacklisting
            "Job Title": "ML Engineer",
            "Location": "Delhi",
            "Salary": "8-12 LPA", 
            "Experience": "4-6 years"
        }
    ]
    
    print("\n2. Processing jobs and building company analytics...")
    for i, job in enumerate(test_jobs, 1):
        print(f"\n--- Processing Job {i} ---")
        company = job["Company"]
        
        # Check real-time blacklist status
        is_blacklisted = client.real_time_blacklist_check(company)
        
        if is_blacklisted:
            print(f"🚫 Skipping {company} - Already blacklisted")
            continue
        
        # Use smart company tracking
        count, was_blacklisted, should_skip = client.smart_company_tracking(company, job)
        
        if should_skip:
            print(f"🚫 {company} was just blacklisted - skipping")
        else:
            print(f"✅ Application would proceed for {job['Job Title']} at {company}")
    
    print("\n3. Testing real-time sync between multiple instances...")
    
    # Simulate another instance
    os.environ['AUTOMATION_INSTANCE_ID'] = 'test_instance_2'
    client2 = CoordinationClient()
    
    print(f"\nInstance 2 checking TCS blacklist status:")
    is_blacklisted = client2.real_time_blacklist_check("TCS")
    print(f"Result: {'🚫 BLACKLISTED' if is_blacklisted else '✅ ALLOWED'}")
    
    print(f"\nInstance 2 getting TCS metadata:")
    metadata = client2.get_company_metadata("TCS")
    if metadata:
        print(f"📊 TCS Analytics:")
        print(f"   Locations: {metadata.get('locations', [])}")
        print(f"   Job titles: {metadata.get('job_titles', [])}")
        print(f"   Total jobs found: {metadata.get('total_jobs_found', 0)}")
        print(f"   Blacklist reason: {metadata.get('blacklist_reason', 'N/A')}")
    
    print("\n4. Enhanced blacklist summary:")
    client2.print_enhanced_blacklist_summary()
    
    print("\n5. Testing shared state file content:")
    try:
        with open('automation_shared_state.json', 'r') as f:
            state = json.load(f)
        
        print(f"\n📄 SHARED STATE FILE CONTENT:")
        print(f"   Blacklisted companies: {len(state.get('blacklisted_companies', []))}")
        print(f"   Company metadata entries: {len(state.get('company_metadata', {}))}")
        print(f"   Company counts: {len(state.get('company_counts', {}))}")
        
        # Show sample metadata
        if state.get('company_metadata'):
            print(f"\n📊 Sample Company Metadata:")
            for company, data in list(state['company_metadata'].items())[:2]:
                print(f"   {company}:")
                print(f"     └─ Jobs found: {data.get('total_jobs_found', 0)}")
                print(f"     └─ Locations: {data.get('locations', [])}")
                print(f"     └─ Applications: {data.get('application_count', 0)}")
    
    except Exception as e:
        print(f"❌ Error reading state file: {e}")
    
    print(f"\n✅ REAL-TIME SYNC TEST COMPLETED!")
    print(f"📊 Key Features Demonstrated:")
    print(f"   🔄 Real-time blacklist synchronization across instances")
    print(f"   📈 Comprehensive company metadata collection")
    print(f"   🚫 Automatic blacklisting with detailed analytics")
    print(f"   📄 Persistent shared state in JSON format")
    print(f"   🤖 Multi-instance coordination")

if __name__ == "__main__":
    test_real_time_sync()
