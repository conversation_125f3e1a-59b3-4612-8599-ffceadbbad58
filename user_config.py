"""
User Configuration Module
Provides user-specific file paths and configurations for all scripts
"""

import os
from dotenv import load_dotenv

class UserConfig:
    def __init__(self):
        # Load environment variables
        load_dotenv()
        
        # Determine current user based on email in .env
        self.current_user = self._detect_current_user()
        
        # User configurations
        self.users = {
            "deepak": {
                "name": "Deepak",
                "data_file": "data/mydata.txt",
                "excel_file": "job_applications_deepak.xlsx"
            },
            "nishka": {
                "name": "Nishka", 
                "data_file": "data/data_nishka.txt",
                "excel_file": "job_applications_nishka.xlsx"
            }
        }
        
    def _detect_current_user(self):
        """Detect current user based on email in environment"""
        email = os.getenv('NAUKRI_EMAIL', '').lower()
        
        if 'deepak' in email or 'garg' in email:
            return 'deepak'
        elif 'nishka' in email or 'mehlawat' in email:
            return 'nishka'
        else:
            # Default to deepak if can't detect
            return 'deepak'
            
    def get_data_file(self):
        """Get the data file path for current user"""
        return self.users[self.current_user]["data_file"]
        
    def get_excel_file(self):
        """Get the Excel file path for current user"""
        return self.users[self.current_user]["excel_file"]
        
    def get_user_name(self):
        """Get the current user's name"""
        return self.users[self.current_user]["name"]
        
    def print_current_config(self):
        """Print current user configuration"""
        print(f"👤 Current User: {self.get_user_name()}")
        print(f"📄 Data File: {self.get_data_file()}")
        print(f"📊 Excel File: {self.get_excel_file()}")
        
# Global instance for easy import
user_config = UserConfig()

def get_data_file():
    """Convenience function to get data file"""
    return user_config.get_data_file()
    
def get_excel_file():
    """Convenience function to get Excel file"""
    return user_config.get_excel_file()
    
def get_user_name():
    """Convenience function to get user name"""
    return user_config.get_user_name()
