"""
API Server for Naukri Job Automation

This module provides a Flask-based API server that uses GPT-2 for generating
personalized responses to job interview questions during automated job applications.

Features:
- Handles experience-related questions with domain-specific responses
- Manages salary expectations and current CTC queries
- Provides technical skills and education information
- Uses GPT-2 model for generating contextual responses
- Environment variable configuration for personalization

Usage:
    python server.py

The server will start on http://localhost:10000 and accept POST requests to /generate
"""

from .server import app

if __name__ == "__main__":
    app.run(host='0.0.0.0', port=10000, debug=False)
