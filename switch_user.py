"""
Quick User Switcher for Naukri Automation
Allows quick switching between users without running full interface
"""

import os
import shutil
import sys

def switch_to_user(user_name):
    """Switch to specified user"""
    env_files = {
        "deepak": ".env.deepak",
        "nishka": ".env.nishka"
    }
    
    user_name = user_name.lower()
    
    if user_name not in env_files:
        print(f"❌ Unknown user: {user_name}")
        print("Available users: deepak, nishka")
        return False
        
    env_file = env_files[user_name]
    
    if not os.path.exists(env_file):
        print(f"❌ Environment file {env_file} not found!")
        return False
        
    try:
        # Copy user environment to main .env
        shutil.copy2(env_file, '.env')
        print(f"✅ Switched to user: {user_name.title()}")
        
        # Show current configuration
        from user_config import user_config
        print(f"👤 Current User: {user_config.get_user_name()}")
        print(f"📄 Data File: {user_config.get_data_file()}")
        print(f"📊 Excel File: {user_config.get_excel_file()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error switching user: {str(e)}")
        return False

def main():
    if len(sys.argv) != 2:
        print("Usage: python switch_user.py <username>")
        print("Available users: deepak, nishka")
        print("\nExamples:")
        print("  python switch_user.py deepak")
        print("  python switch_user.py nishka")
        return
        
    user_name = sys.argv[1]
    switch_to_user(user_name)

if __name__ == "__main__":
    main()
