"""
Main Interface for Naukri Automation System
Simple menu-driven interface using original working logic
"""
import os
import sys
import subprocess
from dotenv import load_dotenv
from user_config import user_config

# Load environment variables
load_dotenv()

def validate_env_vars():
    """Validate required environment variables"""
    required_vars = ['NAUKRI_EMAIL', 'NAUKRI_PASSWORD']
    missing_vars = [var for var in required_vars if not os.getenv(var)]

    if missing_vars:
        print(f"❌ Error: Missing required environment variables: {', '.join(missing_vars)}")
        print("Please check your .env file and ensure all required variables are set.")
        return False
    return True

def run_single_automation():
    """Run single automation using original logic"""
    print("\n🚀 Starting Single Job Application Automation...")

    # Ask user for recommendation or normal jobs
    print("\n📋 Choose automation type:")
    print("1. 🎯 Normal Job Search (Default search with filters)")
    print("2. 💡 Recommended Jobs ")

    while True:
        choice = input("\n👉 Enter your choice (1-2): ").strip()
        if choice in ['1', '2']:
            break
        print("❌ Invalid choice. Please enter 1 or 2.")

    if choice == '2':
        print("\n💡 Starting Recommended Jobs Automation...")
        script_name = "naukri_recommendations_original.py"
    else:
        print("\n🎯 Starting Normal Job Search Automation...")
        script_name = "naukri_automation_original.py"

    try:
        result = subprocess.run([sys.executable, script_name],
                              capture_output=False, text=True)
        if result.returncode == 0:
            print("✅ Single automation completed successfully!")
        else:
            print("❌ Single automation failed!")
    except Exception as e:
        print(f"❌ Error running single automation: {e}")

def run_multiple_automation():
    """Run multiple automation instances"""
    print("\n🚀 Starting Multiple Job Application Automation...")
    print("🧠 Using SMART PARALLEL mode with advanced coordination")
    print("✨ Features:")
    print("   • Chatbot conflict resolution with exclusive locks")
    print("   • Shared radio button response coordination")
    print("   • Real-time instance synchronization")
    print("   • Intelligent resource management")
    try:
        result = subprocess.run([sys.executable, "run_multiple_original.py"],
                              capture_output=False, text=True)
        if result.returncode == 0:
            print("✅ Multiple automation completed successfully!")
        else:
            print("❌ Multiple automation failed!")
    except Exception as e:
        print(f"❌ Error running multiple automation: {e}")

def run_recommendations():
    """Run recommendations automation"""
    print("\n🚀 Starting Recommendations Automation...")
    try:
        result = subprocess.run([sys.executable, "naukri_recommendations_original.py"],
                              capture_output=False, text=True)
        if result.returncode == 0:
            print("✅ Recommendations automation completed successfully!")
        else:
            print("❌ Recommendations automation failed!")
    except Exception as e:
        print(f"❌ Error running recommendations automation: {e}")

def show_menu():
    """Display main menu"""
    print("\n" + "="*60)
    print("🎯 NAUKRI AUTOMATION SYSTEM - MAIN INTERFACE")
    print("="*60)
    print(f"👤 Current User: {user_config.get_user_name()}")
    print(f"📄 Data File: {user_config.get_data_file()}")
    print(f"📊 Excel File: {user_config.get_excel_file()}")
    print("-"*60)
    print("1. 🔍 Run Single Job Search & Application")
    print("2. 🔄 Run Multiple Job Search & Application")
    print("3. 💡 Run Recommendations Automation")
    print("4. 📊 View Job Applications Data")
    print("5. ❌ Exit")
    print("="*60)

def view_data():
    """View job applications data"""
    try:
        excel_file = user_config.get_excel_file()
        if os.path.exists(excel_file):
            print(f"\n📊 Opening job applications data for {user_config.get_user_name()}...")
            os.startfile(excel_file)
        else:
            print(f"\n❌ No job applications data found for {user_config.get_user_name()}. Run automation first.")
    except Exception as e:
        print(f"❌ Error opening data file: {e}")

def select_user_for_main():
    """User selection specifically for main.py"""
    print("\n" + "="*50)
    print("👤 USER SELECTION")
    print("="*50)
    print("\nAvailable Users:")
    print("1. Deepak")
    print("2. Nishka")
    print("\n0. Exit")
    print("-"*50)

    while True:
        choice = input("\nSelect user (0-2): ").strip()

        if choice == "0":
            print("👋 Goodbye!")
            return None
        elif choice == "1":
            return "deepak"
        elif choice == "2":
            return "nishka"
        else:
            print("❌ Invalid choice. Please try again.")

def switch_user_environment(user_name):
    """Switch to the selected user environment"""
    import shutil
    from dotenv import load_dotenv

    env_files = {
        "deepak": ".env.deepak",
        "nishka": ".env.nishka"
    }

    env_file = env_files[user_name]

    if not os.path.exists(env_file):
        print(f"❌ Environment file {env_file} not found!")
        return False

    try:
        # Copy user environment to main .env
        shutil.copy2(env_file, '.env')
        print(f"✅ Switched to user: {user_name.title()}")

        # Reload environment variables
        load_dotenv('.env', override=True)
        print("🔄 Environment variables reloaded")

        print("\n" + "="*60)
        print("⚠️  IMPORTANT: USER SWITCH COMPLETED")
        print("="*60)
        print("🔄 Please restart the application to ensure all")
        print("   modules use the correct user data files.")
        print("💡 Simply run 'python main.py' again.")
        print("="*60)

        return True

    except Exception as e:
        print(f"❌ Error switching user: {str(e)}")
        return False

def setup_user_environment(user_name):
    """Setup environment for selected user"""
    import shutil
    from dotenv import load_dotenv

    env_files = {
        "deepak": ".env.deepak",
        "nishka": ".env.nishka"
    }

    env_file = env_files[user_name]

    if not os.path.exists(env_file):
        print(f"❌ Environment file {env_file} not found!")
        return False

    try:
        # Copy user environment to main .env
        shutil.copy2(env_file, '.env')
        print(f"✅ Switched to user: {user_name.title()}")

        # IMPORTANT: Reload environment variables from the new .env file
        # This clears the current environment and loads the new one
        load_dotenv('.env', override=True)
        print("🔄 Environment variables reloaded")

        # Show current configuration
        user_config.print_current_config()

        return True

    except Exception as e:
        print(f"❌ Error switching user: {str(e)}")
        return False

def main():
    """Main entry point"""
    print("🚀 Welcome to Naukri Automation System!")
    print("📋 Multi-User Support with Original Working Logic")

    # Check if we already have a valid .env file (from previous run or restart)
    if os.path.exists('.env'):
        try:
            from dotenv import load_dotenv
            load_dotenv('.env')

            # Try to detect current user from .env
            current_email = os.getenv('NAUKRI_EMAIL', '').lower()
            if current_email:
                if 'deepak' in current_email or 'garg' in current_email:
                    current_user = 'deepak'
                elif 'nishka' in current_email or 'mehlawat' in current_email:
                    current_user = 'nishka'
                else:
                    current_user = None

                if current_user:
                    print(f"\n✅ Continuing with user: {current_user.title()}")
                    user_config.print_current_config()

                    # Ask if user wants to switch
                    switch = input(f"\nDo you want to switch to a different user? (y/n): ").strip().lower()
                    if switch == 'y':
                        selected_user = select_user_for_main()
                        if selected_user and selected_user != current_user:
                            if switch_user_environment(selected_user):
                                return  # Exit to allow restart
                    # Continue with current user
                else:
                    # Invalid .env, need to select user
                    selected_user = select_user_for_main()
                    if not selected_user:
                        return
                    if switch_user_environment(selected_user):
                        return  # Exit to allow restart
            else:
                # No email in .env, need to select user
                selected_user = select_user_for_main()
                if not selected_user:
                    return
                if switch_user_environment(selected_user):
                    return  # Exit to allow restart
        except:
            # Error loading .env, need to select user
            selected_user = select_user_for_main()
            if not selected_user:
                return
            if switch_user_environment(selected_user):
                return  # Exit to allow restart
    else:
        # No .env file, need to select user
        selected_user = select_user_for_main()
        if not selected_user:
            return
        if switch_user_environment(selected_user):
            return  # Exit to allow restart

    # Validate environment variables
    if not validate_env_vars():
        print("\n💡 Please set up your .env file with required credentials.")
        return

    while True:
        show_menu()
        try:
            choice = input("\n👉 Enter your choice (1-5): ").strip()

            if choice == '1':
                run_single_automation()
            elif choice == '2':
                run_multiple_automation()
            elif choice == '3':
                run_recommendations()
            elif choice == '4':
                view_data()
            elif choice == '5':
                print("\n👋 Thank you for using Naukri Automation System!")
                break
            else:
                print("\n❌ Invalid choice! Please enter 1-5.")

        except KeyboardInterrupt:
            print("\n\n👋 Thank you for using Naukri Automation System!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    main()
