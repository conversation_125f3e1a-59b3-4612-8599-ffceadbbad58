#!/usr/bin/env python3
"""
Security check script to scan for hardcoded credentials
Run this before committing to ensure no sensitive data is included
"""

import os
import re
from pathlib import Path

# Patterns to look for suspicious content
SENSITIVE_PATTERNS = [
    r'[a-zA-Z0-9._%+-]+@gmail\.com',  # <PERSON>ail addresses
    r'password\s*[=:]\s*["\'][^"\']+["\']',  # Password assignments
    r'badshaah|Deepak@2212|badshsaah',  # Known passwords
    r'gargdeepak114',  # Personal identifier
    r'\+91[\s-]?[0-9]{10}',  # Phone numbers
    r'Bennett University',  # Personal university
    r'Faridabad|Haryana',  # Personal location
]

# Files to exclude from scanning
EXCLUDE_PATTERNS = [
    r'\.env$',
    r'\.env\..*',
    r'__pycache__',
    r'\.git',
    r'myenv',
    r'\.xlsx?$',
    r'\.pdf$',
    r'\.pkl$',
    r'\.json$',
    r'cookies',
    r'node_modules',
    r'setup\.py$',  # This file itself
    r'security_check\.py$',  # This file
]

def should_exclude_file(file_path):
    """Check if file should be excluded from scanning"""
    file_str = str(file_path)
    return any(re.search(pattern, file_str, re.IGNORECASE) for pattern in EXCLUDE_PATTERNS)

def scan_file(file_path):
    """Scan a single file for sensitive patterns"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        findings = []
        for line_num, line in enumerate(content.split('\n'), 1):
            for pattern in SENSITIVE_PATTERNS:
                matches = re.finditer(pattern, line, re.IGNORECASE)
                for match in matches:
                    # Skip if it's in a comment showing example/template
                    if any(keyword in line.lower() for keyword in ['example', 'template', 'your_', 'sample']):
                        continue
                    
                    findings.append({
                        'line': line_num,
                        'text': line.strip(),
                        'match': match.group(),
                        'pattern': pattern
                    })
        
        return findings
    except Exception as e:
        print(f"Error scanning {file_path}: {e}")
        return []

def main():
    """Main security check function"""
    print("🔒 Security Check: Scanning for hardcoded credentials...")
    print("=" * 60)
    
    total_files = 0
    files_with_issues = 0
    total_issues = 0
    
    # Get all Python files in the project
    for file_path in Path('.').rglob('*.py'):
        if should_exclude_file(file_path):
            continue
        
        total_files += 1
        findings = scan_file(file_path)
        
        if findings:
            files_with_issues += 1
            total_issues += len(findings)
            
            print(f"\n⚠️  Found issues in: {file_path}")
            for finding in findings:
                print(f"   Line {finding['line']}: {finding['match']}")
                print(f"   Code: {finding['text'][:100]}...")
    
    # Also check other text files
    for pattern in ['*.txt', '*.md', '*.json']:
        for file_path in Path('.').rglob(pattern):
            if should_exclude_file(file_path):
                continue
            
            # Only check specific files that might contain sensitive data
            if file_path.name.lower() in ['readme.md', 'config.json']:
                total_files += 1
                findings = scan_file(file_path)
                
                if findings:
                    files_with_issues += 1
                    total_issues += len(findings)
                    
                    print(f"\n⚠️  Found issues in: {file_path}")
                    for finding in findings:
                        print(f"   Line {finding['line']}: {finding['match']}")
    
    print("\n" + "=" * 60)
    print(f"📊 Security Check Results:")
    print(f"   Files scanned: {total_files}")
    print(f"   Files with issues: {files_with_issues}")
    print(f"   Total issues found: {total_issues}")
    
    if total_issues == 0:
        print("\n✅ No hardcoded credentials found! Safe to commit.")
        return True
    else:
        print("\n❌ Security issues found! Please review and fix before committing.")
        print("\n💡 Tips:")
        print("   - Move credentials to .env file")
        print("   - Use environment variables in code")
        print("   - Add sensitive files to .gitignore")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
