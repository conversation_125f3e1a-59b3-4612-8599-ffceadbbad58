#!/usr/bin/env python3
"""
Setup script for Naukri Job Application Automation
This script helps users configure their environment variables safely.
"""

import os
import shutil
from pathlib import Path

def setup_environment():
    """Setup the environment for the Naukri automation project."""
    
    print("🚀 Setting up Naukri Job Application Automation")
    print("=" * 50)
    
    # Check if .env file exists
    env_file = Path('.env')
    env_example = Path('.env.example')
    
    if env_file.exists():
        print("✅ .env file already exists")
        overwrite = input("Do you want to overwrite it? (y/N): ").lower().strip()
        if overwrite != 'y':
            print("Setup cancelled. Existing .env file preserved.")
            return
    
    # Copy .env.example to .env
    if env_example.exists():
        shutil.copy('.env.example', '.env')
        print("✅ Created .env file from template")
    else:
        print("❌ .env.example file not found")
        return
    
    print("\n📝 Please edit the .env file with your personal information:")
    print("   - NAUKRI_EMAIL: Your email address")
    print("   - NAUKRI_PASSWORD: Your password")
    print("   - FIRST_NAME, LAST_NAME: Your name")
    print("   - PHONE: Your phone number")
    print("   - ADDRESS: Your address")
    print("   - And other personal details...")
    
    print("\n⚠️  Security Reminders:")
    print("   - Never commit the .env file to version control")
    print("   - Keep your credentials secure")
    print("   - The .gitignore file is configured to exclude sensitive files")
    
    print("\n🔧 Next steps:")
    print("   1. Edit the .env file with your information")
    print("   2. Install dependencies: pip install -r requirements.txt")
    print("   3. Run the automation: python naukri_automation.py")
    
    print("\n✨ Setup completed!")

def validate_setup():
    """Validate that the environment is properly configured."""
    
    print("🔍 Validating setup...")
    
    # Check for .env file
    if not Path('.env').exists():
        print("❌ .env file not found. Run setup first.")
        return False
    
    # Check for required dependencies
    try:
        import selenium
        import pandas
        import requests
        from dotenv import load_dotenv
        print("✅ All required packages are installed")
    except ImportError as e:
        print(f"❌ Missing package: {e}")
        print("Run: pip install -r requirements.txt")
        return False
    
    # Load and check environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    required_vars = ['NAUKRI_EMAIL', 'NAUKRI_PASSWORD']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var) or os.getenv(var) == f'your_{var.lower()}':
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Please set the following environment variables in .env: {', '.join(missing_vars)}")
        return False
    
    print("✅ Environment validation passed!")
    return True

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'validate':
        validate_setup()
    else:
        setup_environment()
