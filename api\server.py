import transformers
import torch
from flask import Flask, request, jsonify
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

app = Flask(__name__)

model_id = "openai-community/gpt2"

# Read the content from the 'mydata.txt' file
try:
    with open("data/mydata.txt", "r") as file:
        content = file.read()
except FileNotFoundError:
    content = "Default content as the file was not found."

try:
    pipeline = transformers.pipeline(
        "text-generation",
        model=model_id,
        device_map="auto",
    )
except Exception as e:
    pipeline = None
    print(f"Error loading model: {e}")

@app.route('/generate', methods=['POST'])
def generate():
    if pipeline is None:
        return jsonify({"error": "Model not loaded"}), 500

    data = request.json
    if not data or 'message' not in data:
        return jsonify({"error": "Invalid input"}), 400

    user_message = data.get('message', '')

    # Define specialized experience categories
    def get_experience_prompt(domain):
        domain_experience = {
            'data science': '3 years',
            'machine learning': '2.5 years',
            'artificial intelligence': '2 years',
            'deep learning': '1.5 years',
            'natural language processing': '1 year',
            'python': '3 years',
            'aws': '2 years',
            'tableau': '2 years',
            'power bi': '1.5 years',
            'mysql': '2.5 years'
        }
        
        experience = domain_experience.get(domain, '3 years')
        return f"""Name: Deepak Garg
Professional Profile: Data Science & ML Developer with 3 years total experience
Company: Appsquadz

Experience Breakdown:
- Data Science: 3 years
- Machine Learning: 2.5 years  
- Artificial Intelligence: 2 years
- Deep Learning: 1.5 years
- Natural Language Processing: 1 year
- Python Programming: 3 years
- AWS: 2 years
- Tableau: 2 years
- Power BI: 1.5 years
- MySQL: 2.5 years

Question: How many years of experience do you have in {domain}?
Answer: {experience} of experience in {domain}"""

    # More specific keyword detection
    user_lower = user_message.lower()
    
    # Experience-related questions with domain specificity
    if 'experience' in user_lower or 'years' in user_lower:
        if 'data science' in user_lower:
            prompt = get_experience_prompt('data science')
        elif 'machine learning' in user_lower or 'ml' in user_lower:
            prompt = get_experience_prompt('machine learning')
        elif 'artificial intelligence' in user_lower or 'ai' in user_lower:
            prompt = get_experience_prompt('artificial intelligence')
        elif 'deep learning' in user_lower or 'dl' in user_lower:
            prompt = get_experience_prompt('deep learning')
        elif 'natural language processing' in user_lower or 'nlp' in user_lower:
            prompt = get_experience_prompt('natural language processing')
        elif 'python' in user_lower:
            prompt = get_experience_prompt('python')
        elif 'aws' in user_lower:
            prompt = get_experience_prompt('aws')
        elif 'tableau' in user_lower:
            prompt = get_experience_prompt('tableau')
        elif 'power bi' in user_lower:
            prompt = get_experience_prompt('power bi')
        elif 'mysql' in user_lower or 'sql' in user_lower:
            prompt = get_experience_prompt('mysql')
        else:
            prompt = get_experience_prompt('data science')
            
    # Salary-related questions
    elif 'expected' in user_lower and ('salary' in user_lower or 'ctc' in user_lower):
        prompt = f"""Name: Deepak Garg
Expected Salary: 8 LPA
Current Salary: 5 LPA

Question: What is your expected CTC/salary?
Answer: My expected CTC is 8 LPA"""
        
    elif 'current' in user_lower and ('salary' in user_lower or 'ctc' in user_lower):
        prompt = f"""Name: Deepak Garg
Current Salary: 5 LPA

Question: What is your current CTC/salary?
Answer: 5 LPA"""
        
    elif 'skill' in user_lower or 'technology' in user_lower or 'tools' in user_lower:
        prompt = f"""Name: Deepak Garg
Technical Skills: 
- Programming: Python, R, SQL
- Machine Learning: Scikit-learn, TensorFlow, PyTorch
- Cloud: AWS (EC2, S3, Lambda, RDS)
- Data Visualization: Tableau, Power BI, Matplotlib, Seaborn
- Database: MySQL, PostgreSQL, MongoDB
- Big Data: Spark, Hadoop
- Tools: Git, Docker, Jupyter

Question: What are your technical skills?
Answer: I am skilled in Python, R, SQL, Machine Learning frameworks like TensorFlow and PyTorch, AWS cloud services, data visualization tools like Tableau and Power BI, and various databases including MySQL and PostgreSQL"""
        
    elif 'location' in user_lower or 'where' in user_lower:
        prompt = f"""Name: Deepak Garg
Current Location: {os.getenv('ADDRESS', 'Delhi, India')}

Question: Where are you located?
Answer: I am currently located in {os.getenv('ADDRESS', 'Delhi, India')}"""
        
    elif 'notice' in user_lower and 'period' in user_lower:
        prompt = f"""Name: Deepak Garg
Notice Period: 15 days

Question: What is your notice period?
Answer: My notice period is 15 days"""
                
    elif 'education' in user_lower or 'degree' in user_lower:
        prompt = f"""Name: Deepak Garg
Education: {os.getenv('DEGREE', 'Bachelor of Technology')} in {os.getenv('MAJOR', 'Computer Science')} from {os.getenv('UNIVERSITY', 'Delhi University')}
CGPA: 8.06

Question: What is your educational background?
Answer:  completed {os.getenv('DEGREE', 'Bachelor of Technology')} in {os.getenv('MAJOR', 'Computer Science')} from {os.getenv('UNIVERSITY', 'Delhi University')} with 8.06 CGPA"""
            
    else:
        # Generic response using resume data
        prompt = f"""I am Deepak Garg, a Data Science & ML Developer with 3 years of experience working at Appsquadz.

My expertise includes:
- Data Science and Machine Learning
- Python Programming
- AWS Cloud Services
- Data Visualization (Tableau, Power BI)
- Database Management (MySQL, PostgreSQL)

Question: {user_message}
Answer: As Deepak Garg,"""

    try:
        outputs = pipeline(
            prompt,
            max_new_tokens=15,  # Even shorter for more focused responses
            num_return_sequences=1,
            pad_token_id=pipeline.tokenizer.eos_token_id,
            do_sample=False,  # Use greedy decoding for consistency
            temperature=0.1,  # Very low temperature
            early_stopping=True
        )
        
        # Extract only the generated part
        full_text = outputs[0]["generated_text"]
        generated_text = full_text[len(prompt):].strip()
        
        # Intelligent response handling based on question type
        if 'experience' in user_lower or 'years' in user_lower:
            if 'data science' in user_lower:
                final_answer = " 3 years"
            elif 'machine learning' in user_lower or 'ml' in user_lower:
                final_answer = " 2.5 years"
            elif 'artificial intelligence' in user_lower or 'ai' in user_lower:
                final_answer = " 3 years "
            elif 'deep learning' in user_lower or 'dl' in user_lower:
                final_answer = " 2.5 years"
            elif 'natural language processing' in user_lower or 'nlp' in user_lower:
                final_answer = " 2 year "
            elif 'python' in user_lower:
                final_answer = " 3 years"
            elif 'aws' in user_lower:
                final_answer = " 2.5 years"
            elif 'tableau' in user_lower:
                final_answer = " 2.5 years "
            elif 'power bi' in user_lower:
                final_answer = " 3 years"
            elif 'mysql' in user_lower or 'sql' in user_lower:
                final_answer = " 2.5 years "
            else:
                final_answer = " 3 years"
        
        elif 'expected' in user_lower and ('salary' in user_lower or 'ctc' in user_lower):
            final_answer = "8 "
        
        elif 'current' in user_lower and ('salary' in user_lower or 'ctc' in user_lower):
            final_answer = "5 "
        
        elif 'skill' in user_lower or 'technology' in user_lower or 'tools' in user_lower:
            final_answer = "I am skilled in Python, SQL, Machine Learning, TensorFlow, PyTorch, and AWS."
        
        elif 'location' in user_lower or 'where' in user_lower:
            final_answer = f"I am currently located in {os.getenv('ADDRESS', 'Delhi, India')}."
        
        elif 'notice' in user_lower and 'period' in user_lower:
            final_answer = "15 days."
        
        elif 'education' in user_lower or 'degree' in user_lower:
            final_answer = f" completed {os.getenv('DEGREE', 'Bachelor of Technology')} in {os.getenv('MAJOR', 'Computer Science')} from {os.getenv('UNIVERSITY', 'Delhi University')} with 8.06 CGPA."

        elif 'interested' in user_lower or 'contractual' in user_lower or 'contract' in user_lower:
            final_answer = "Yes, I am interested."

        elif 'azure' in user_lower and 'gcp' in user_lower:
            final_answer = "Yes,  experience with both Azure and GCP."

        elif 'azure' in user_lower:
            final_answer = "Yes,  experience with Azure."

        elif 'gcp' in user_lower or 'google cloud' in user_lower:
            final_answer = "Yes,  experience with GCP."

        elif 'tensorflow' in user_lower and 'pytorch' in user_lower:
            final_answer = "Yes,  experience with both TensorFlow and PyTorch."

        elif 'tensorflow' in user_lower:
            final_answer = "Yes,  experience with TensorFlow."

        elif 'pytorch' in user_lower:
            final_answer = "Yes,  experience with PyTorch."

        elif 'lwd' in user_lower or 'last working day' in user_lower:
            final_answer = "np is 15 days."

        else:
            # Use model-generated response for generic questions
            if generated_text:
                # Clean up generated text
                if '\n' in generated_text:
                    generated_text = generated_text.split('\n')[0]
                if '.' in generated_text and len(generated_text.split('.')) > 1:
                    sentences = generated_text.split('.')
                    generated_text = sentences[0] + '.'
                final_answer = generated_text
            else:
                final_answer = "Yes, I am interested and available."
        
        return jsonify({"response": {"content": final_answer}})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=10000)
