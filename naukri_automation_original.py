from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, StaleElementReferenceException, WebDriverException, NoSuchElementException
import time
import pandas as pd
import requests
import re
import os
import sys
import json
import threading
from dotenv import load_dotenv
from datetime import datetime
from user_config import get_excel_file, get_data_file, get_user_name
import random

# Load environment variables
load_dotenv()

# Constants
MAX_RETRIES = 3
PAGE_LOAD_TIMEOUT = 30
APPLICATION_RETRY_ATTEMPTS = 1  # Number of retry attempts for failed applications
API_URL = "http://localhost:10000/generate"
# Store job applications data
applications = []
excel_lock = threading.Lock()  # For thread-safe Excel operations

# Coordination system
INSTANCE_ID = os.getenv('AUTOMATION_INSTANCE_ID', f'standalone_{int(time.time())}')
COORDINATION_ENABLED = os.getenv('AUTOMATION_COORDINATION_ENABLED', 'false').lower() == 'true'
STATE_FILE = os.getenv('AUTOMATION_STATE_FILE', 'automation_shared_state.json')

class CoordinationClient:
    """Client for coordinating with other automation instances"""
    
    def __init__(self):
        self.instance_id = INSTANCE_ID
        self.enabled = COORDINATION_ENABLED
        self.state_file = STATE_FILE
        
    def request_chatbot_lock(self):
        """Request exclusive lock for chatbot handling"""
        if not self.enabled:
            return True
            
        max_wait = 300  # 5 minutes max wait
        wait_time = 0
        
        while wait_time < max_wait:
            try:
                state = self._read_state()
                if not state.get("chatbot_in_progress", False):
                    state["chatbot_in_progress"] = True
                    state["chatbot_handler"] = self.instance_id
                    self._write_state(state)
                    print(f"🤖 {self.instance_id} acquired chatbot lock")
                    return True
            except:
                pass
            
            time.sleep(2)
            wait_time += 2
            if wait_time % 30 == 0:
                print(f"⏳ {self.instance_id} waiting for chatbot lock... ({wait_time}s)")
        
        print(f"⏰ {self.instance_id} timeout waiting for chatbot lock")
        return False
    
    def release_chatbot_lock(self):
        """Release chatbot lock"""
        if not self.enabled:
            return
            
        try:
            state = self._read_state()
            if state.get("chatbot_handler") == self.instance_id:
                state["chatbot_in_progress"] = False
                state["chatbot_handler"] = None
                self._write_state(state)
                print(f"🔓 {self.instance_id} released chatbot lock")
        except:
            pass
    
    def request_excel_lock(self):
        """Request exclusive lock for Excel file operations"""
        if not self.enabled:
            return True
            
        max_wait = 120  # 2 minutes max wait for Excel operations
        wait_time = 0
        
        while wait_time < max_wait:
            try:
                state = self._read_state()
                if not state.get("excel_in_progress", False):
                    state["excel_in_progress"] = True
                    state["excel_handler"] = self.instance_id
                    self._write_state(state)
                    print(f"📊 {self.instance_id} acquired Excel lock")
                    return True
            except:
                pass
            
            time.sleep(1)
            wait_time += 1
            if wait_time % 15 == 0:
                print(f"⏳ {self.instance_id} waiting for Excel lock... ({wait_time}s)")
        
        print(f"⏰ {self.instance_id} timeout waiting for Excel lock")
        return False
    
    def release_excel_lock(self):
        """Release Excel lock"""
        if not self.enabled:
            return
            
        try:
            state = self._read_state()
            if state.get("excel_handler") == self.instance_id:
                state["excel_in_progress"] = False
                state["excel_handler"] = None
                self._write_state(state)
                print(f"🔓 {self.instance_id} released Excel lock")
        except:
            pass
    
    def update_applications_count(self, count):
        """Update the total applications saved count"""
        if not self.enabled:
            return
            
        try:
            state = self._read_state()
            state["total_applications_saved"] = state.get("total_applications_saved", 0) + count
            self._write_state(state)
            print(f"📈 Total applications saved across all instances: {state['total_applications_saved']}")
        except:
            pass

    def get_radio_response(self, question_type):
        """Get predefined response for radio button questions"""
        if not self.enabled:
            return None
            
        try:
            state = self._read_state()
            responses = state.get("radio_button_responses", {})
            return responses.get(question_type.lower(), None)
        except:
            return None
    
    def _read_state(self):
        """Read shared state"""
        if not os.path.exists(self.state_file):
            return {}
        try:
            with open(self.state_file, 'r') as f:
                return json.load(f)
        except:
            return {}
    
    def _write_state(self, state):
        """Write shared state"""
        try:
            with open(self.state_file, 'w') as f:
                json.dump(state, f, indent=2)
        except:
            pass

    def is_company_blacklisted(self, company_name):
        """Check if a company is blacklisted using JSON coordination"""
        if not self.enabled:
            return False
            
        try:
            state = self._read_state()
            blacklisted_companies = state.get("blacklisted_companies", [])
            
            # Normalize company name for comparison
            normalized_company = company_name.strip().lower()
            normalized_blacklist = [c.strip().lower() for c in blacklisted_companies]
            
            is_blacklisted = normalized_company in normalized_blacklist
            if is_blacklisted:
                print(f"🚫 {company_name} is blacklisted")
            
            return is_blacklisted
            
        except Exception as e:
            print(f"⚠️ Error checking blacklist: {e}")
            return False

    def add_company_application(self, company_name):
        """
        Add a company application and check if it should be blacklisted
        Returns: (new_count, was_blacklisted)
        """
        if not self.enabled:
            return 1, False
            
        try:
            state = self._read_state()
            
            # Initialize if not exists
            if "company_counts" not in state:
                state["company_counts"] = {}
            if "blacklisted_companies" not in state:
                state["blacklisted_companies"] = []
            if "max_applications_per_company" not in state:
                state["max_applications_per_company"] = 2
            
            # Get current count and increment
            current_count = state["company_counts"].get(company_name, 0)
            new_count = current_count + 1
            state["company_counts"][company_name] = new_count
            
            max_allowed = state["max_applications_per_company"]
            was_blacklisted = False
            
            # Check if company should be blacklisted
            if new_count >= max_allowed:
                if company_name not in state["blacklisted_companies"]:
                    state["blacklisted_companies"].append(company_name)
                    was_blacklisted = True
                    print(f"🚫 {company_name} added to blacklist (reached {max_allowed} applications)")
                else:
                    print(f"🚫 {company_name} already blacklisted")
            else:
                print(f"📊 {company_name}: {new_count}/{max_allowed} applications")
            
            # Save updated state
            self._write_state(state)
            
            return new_count, was_blacklisted
            
        except Exception as e:
            print(f"⚠️ Error managing company application: {e}")
            return 1, False

    def add_to_blacklist(self, company_name):
        """Manually add a company to blacklist"""
        if not self.enabled:
            return
            
        try:
            state = self._read_state()
            
            if "blacklisted_companies" not in state:
                state["blacklisted_companies"] = []
            
            if company_name not in state["blacklisted_companies"]:
                state["blacklisted_companies"].append(company_name)
                self._write_state(state)
                print(f"🚫 Manually added {company_name} to blacklist")
            else:
                print(f"🚫 {company_name} already in blacklist")
            
        except Exception as e:
            print(f"⚠️ Error adding to blacklist: {e}")

    def get_company_stats(self):
        """Get current company statistics"""
        if not self.enabled:
            return {}, []
            
        try:
            state = self._read_state()
            company_counts = state.get("company_counts", {})
            blacklisted_companies = state.get("blacklisted_companies", [])
            
            return company_counts, blacklisted_companies
            
        except Exception as e:
            print(f"⚠️ Error getting company stats: {e}")
            return {}, []

    def sync_blacklist_state(self):
        """Synchronize blacklist state from shared JSON file in real-time"""
        if not self.enabled:
            return {}, []
            
        try:
            state = self._read_state()
            blacklisted_companies = state.get("blacklisted_companies", [])
            company_counts = state.get("company_counts", {})
            company_metadata = state.get("company_metadata", {})
            
            return {
                "blacklisted_companies": blacklisted_companies,
                "company_counts": company_counts,
                "company_metadata": company_metadata,
                "last_sync": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            print(f"⚠️ Error syncing blacklist state: {e}")
            return {}, []

    def update_company_metadata(self, company_name, metadata):
        """Update comprehensive company metadata in shared state"""
        if not self.enabled:
            return
            
        try:
            state = self._read_state()
            
            if "company_metadata" not in state:
                state["company_metadata"] = {}
            
            # Normalize company name for consistent storage
            normalized_name = company_name.strip()
            
            # Store comprehensive metadata
            state["company_metadata"][normalized_name] = {
                "name": normalized_name,
                "normalized_name": normalized_name.lower(),
                "first_seen": metadata.get("first_seen", datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
                "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "locations": list(set(metadata.get("locations", []))),
                "job_titles": list(set(metadata.get("job_titles", []))),
                "salary_ranges": list(set(metadata.get("salary_ranges", []))),
                "application_count": state.get("company_counts", {}).get(normalized_name, 0),
                "is_blacklisted": normalized_name in state.get("blacklisted_companies", []),
                "blacklist_reason": metadata.get("blacklist_reason", ""),
                "total_jobs_found": metadata.get("total_jobs_found", 1),
                "success_rate": metadata.get("success_rate", 0.0),
                "avg_response_time": metadata.get("avg_response_time", "Unknown"),
                "instance_encounters": metadata.get("instance_encounters", [self.instance_id])
            }
            
            self._write_state(state)
            print(f"📊 Updated metadata for {normalized_name}")
            
        except Exception as e:
            print(f"⚠️ Error updating company metadata: {e}")

    def get_company_metadata(self, company_name):
        """Get comprehensive company metadata from shared state"""
        if not self.enabled:
            return {}
            
        try:
            state = self._read_state()
            company_metadata = state.get("company_metadata", {})
            normalized_name = company_name.strip()
            
            return company_metadata.get(normalized_name, {})
            
        except Exception as e:
            print(f"⚠️ Error getting company metadata: {e}")
            return {}

    def real_time_blacklist_check(self, company_name):
        """Real-time blacklist check with fresh state sync"""
        if not self.enabled:
            return False
            
        try:
            # Always get fresh state for real-time checking
            fresh_state = self._read_state()
            blacklisted_companies = fresh_state.get("blacklisted_companies", [])
            
            # Normalize company name for comparison
            normalized_company = company_name.strip().lower()
            normalized_blacklist = [c.strip().lower() for c in blacklisted_companies]
            
            is_blacklisted = normalized_company in normalized_blacklist
            
            if is_blacklisted:
                print(f"🚫 REAL-TIME CHECK: {company_name} is blacklisted")
                # Get blacklist reason if available
                metadata = self.get_company_metadata(company_name)
                reason = metadata.get("blacklist_reason", "Application limit reached")
                print(f"   Reason: {reason}")
            
            return is_blacklisted
            
        except Exception as e:
            print(f"⚠️ Error in real-time blacklist check: {e}")
            return False

    def smart_company_tracking(self, company_name, job_metadata):
        """
        Enhanced company tracking with metadata collection and real-time blacklisting
        Returns: (new_count, was_blacklisted, should_skip)
        """
        if not self.enabled:
            return 1, False, False
            
        try:
            state = self._read_state()
            
            # Initialize state components
            if "company_counts" not in state:
                state["company_counts"] = {}
            if "blacklisted_companies" not in state:
                state["blacklisted_companies"] = []
            if "company_metadata" not in state:
                state["company_metadata"] = {}
            if "max_applications_per_company" not in state:
                state["max_applications_per_company"] = 2
            
            normalized_name = company_name.strip()
            
            # Real-time blacklist check first
            if self.real_time_blacklist_check(company_name):
                return 0, True, True  # Skip this company
            
            # Get current count and increment
            current_count = state["company_counts"].get(normalized_name, 0)
            new_count = current_count + 1
            state["company_counts"][normalized_name] = new_count
            
            max_allowed = state["max_applications_per_company"]
            was_blacklisted = False
            
            # Update comprehensive metadata
            existing_metadata = state["company_metadata"].get(normalized_name, {})
            
            # Collect new metadata
            locations = existing_metadata.get("locations", [])
            if job_metadata.get("Location", "Unknown") != "Unknown":
                locations.append(job_metadata["Location"])
            
            job_titles = existing_metadata.get("job_titles", [])
            if job_metadata.get("Job Title", "Unknown") != "Unknown":
                job_titles.append(job_metadata["Job Title"])
            
            salary_ranges = existing_metadata.get("salary_ranges", [])
            if job_metadata.get("Salary", "Unknown") != "Unknown":
                salary_ranges.append(job_metadata["Salary"])
            
            # Update metadata
            updated_metadata = {
                "locations": list(set(locations)),
                "job_titles": list(set(job_titles)),
                "salary_ranges": list(set(salary_ranges)),
                "total_jobs_found": existing_metadata.get("total_jobs_found", 0) + 1,
                "first_seen": existing_metadata.get("first_seen", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
            }
            
            # Check if company should be blacklisted
            if new_count >= max_allowed:
                if normalized_name not in state["blacklisted_companies"]:
                    state["blacklisted_companies"].append(normalized_name)
                    was_blacklisted = True
                    
                    # Add blacklist reason to metadata
                    updated_metadata["blacklist_reason"] = f"Reached {max_allowed} applications limit"
                    updated_metadata["blacklisted_date"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    
                    print(f"🚫 {normalized_name} BLACKLISTED (reached {max_allowed} applications)")
                    print(f"   📊 Company had {updated_metadata['total_jobs_found']} total job postings")
                    print(f"   📍 Locations: {', '.join(updated_metadata['locations'][:3])}...")
                else:
                    print(f"🚫 {normalized_name} already blacklisted")
            else:
                print(f"📊 {normalized_name}: {new_count}/{max_allowed} applications")
                print(f"   📍 Found in: {job_metadata.get('Location', 'Unknown')}")
            
            # Update company metadata
            self.update_company_metadata(normalized_name, updated_metadata)
            
            # Save updated state
            self._write_state(state)
            
            return new_count, was_blacklisted, False
            
        except Exception as e:
            print(f"⚠️ Error in smart company tracking: {e}")
            return 1, False, False

    def print_enhanced_blacklist_summary(self):
        """Print enhanced blacklist and company analytics summary"""
        if not self.enabled:
            print("📊 Coordination disabled - no shared blacklist data")
            return
            
        try:
            sync_data = self.sync_blacklist_state()
            company_counts = sync_data.get("company_counts", {})
            blacklisted_companies = sync_data.get("blacklisted_companies", [])
            company_metadata = sync_data.get("company_metadata", {})
            
            print(f"\n📊 ENHANCED COMPANY ANALYTICS & BLACKLIST:")
            print(f"   🚫 Blacklisted companies: {len(blacklisted_companies)}")
            print(f"   📈 Total companies tracked: {len(company_counts)}")
            print(f"   🔄 Last sync: {sync_data.get('last_sync', 'Never')}")
            print(f"   🤖 Instance ID: {self.instance_id}")
            
            if blacklisted_companies:
                print(f"\n🚫 BLACKLISTED COMPANIES WITH ANALYTICS:")
                for company in blacklisted_companies:
                    count = company_counts.get(company, 0)
                    metadata = company_metadata.get(company, {})
                    locations = metadata.get("locations", [])
                    job_titles = metadata.get("job_titles", [])
                    reason = metadata.get("blacklist_reason", "Unknown")
                    
                    print(f"   • {company}")
                    print(f"     ├─ Applications: {count}")
                    print(f"     ├─ Reason: {reason}")
                    print(f"     ├─ Job postings found: {metadata.get('total_jobs_found', 0)}")
                    print(f"     ├─ Locations: {', '.join(locations[:3])}{'...' if len(locations) > 3 else ''}")
                    print(f"     └─ Job types: {', '.join(job_titles[:2])}{'...' if len(job_titles) > 2 else ''}")
            
            if company_counts:
                print(f"\n📈 TOP ACTIVE COMPANIES (Not Blacklisted):")
                active_companies = {k: v for k, v in company_counts.items() if k not in blacklisted_companies}
                sorted_active = sorted(active_companies.items(), key=lambda x: x[1], reverse=True)
                
                for company, count in sorted_active[:5]:  # Top 5 active
                    metadata = company_metadata.get(company, {})
                    locations = metadata.get("locations", [])
                    print(f"   • {company}: {count} applications")
                    print(f"     └─ Active in: {', '.join(locations[:2])}{'...' if len(locations) > 2 else ''}")
            
            # Show system statistics
            total_applications = sum(company_counts.values())
            avg_apps_per_company = total_applications / len(company_counts) if company_counts else 0
            
            print(f"\n📊 SYSTEM STATISTICS:")
            print(f"   📈 Total applications submitted: {total_applications}")
            print(f"   📊 Average applications per company: {avg_apps_per_company:.1f}")
            print(f"   🎯 Blacklist efficiency: {len(blacklisted_companies)}/{len(company_counts)} companies controlled")
            
        except Exception as e:
            print(f"⚠️ Error printing enhanced summary: {e}")

# Initialize coordination client
coordination_client = CoordinationClient()

def safe_append_to_excel(new_data, filename=None):
    """Enhanced thread-safe and process-safe Excel append with JSON-based company tracking"""
    if filename is None:
        filename = get_excel_file()
    
    print(f"📊 {INSTANCE_ID} attempting to save {len(new_data)} applications to {filename}")
    
    # Try to get coordination lock, but don't fail if we can't
    has_coordination_lock = coordination_client.request_excel_lock()
    if not has_coordination_lock:
        print(f"⚠️ {INSTANCE_ID} proceeding without coordination lock - using local file locking")
    
    try:
        with excel_lock:
            print(f"📊 {INSTANCE_ID} processing {len(new_data)} applications for Excel save")
            
            # Filter new data using JSON-based coordination (if available) or local logic
            filtered_data = []
            skipped_stats = {
                "blacklisted": 0,
                "duplicates": 0,
                "external": 0,
                "applied": 0
            }
            
            # Load existing data for duplicate checking
            existing_jobs = set()
            if os.path.exists(filename):
                try:
                    existing_df = pd.read_excel(filename)
                    # Create unique identifiers for existing jobs
                    for _, row in existing_df.iterrows():
                        job_id = f"{row.get('Company', '')}-{row.get('Job Title', '')}-{row.get('Location', '')}".lower()
                        existing_jobs.add(job_id)
                except Exception as e:
                    print(f"⚠️ Error loading existing data: {e}")
            
            for record in new_data:
                company = record.get('Company', 'Unknown')
                job_title = record.get('Job Title', 'Unknown')
                location = record.get('Location', 'Unknown')
                
                # Create unique identifier for duplicate checking
                job_id = f"{company}-{job_title}-{location}".lower()
                
                # Check for exact duplicates first
                if job_id in existing_jobs:
                    print(f"🔄 Duplicate detected: {job_title} at {company}")
                    skipped_stats["duplicates"] += 1
                    continue
                
                # Check if company is blacklisted (using real-time check if coordination available)
                if has_coordination_lock:
                    if coordination_client.real_time_blacklist_check(company):
                        print(f"🚫 Skipping {job_title} at {company} - Company blacklisted (real-time)")
                        skipped_stats["blacklisted"] += 1
                        continue
                
                # Skip external applications to prevent large files
                if record.get('Status', '').lower() in ['external application required', 'company site']:
                    print(f"⚠️ Skipping {job_title} at {company} - External application")
                    skipped_stats["external"] += 1
                    continue
                
                # Update company count only if we have coordination lock
                if has_coordination_lock:
                    new_count, was_blacklisted = coordination_client.add_company_application(company)
                    
                    if was_blacklisted:
                        print(f"🚫 {company} was just blacklisted - skipping this application")
                        skipped_stats["blacklisted"] += 1
                        continue
                    
                    print(f"✅ APPLYING: {job_title} at {company} (Application #{new_count})")
                else:
                    print(f"✅ APPLYING: {job_title} at {company} (No coordination)")
                
                # Application is approved
                filtered_data.append(record)
                skipped_stats["applied"] += 1
            
            # Print filtering summary
            print(f"\n📊 FILTERING SUMMARY:")
            print(f"   ✅ Applied: {skipped_stats['applied']}")
            print(f"   🔄 Duplicates: {skipped_stats['duplicates']}")
            print(f"   🚫 Blacklisted: {skipped_stats['blacklisted']}")
            print(f"   ⚠️ External: {skipped_stats['external']}")
            
            if not filtered_data:
                print(f"🚫 {INSTANCE_ID} no data to save after filtering")
                return
            
            # Always try to append to the main file
            try:
                # Combine with existing data
                if os.path.exists(filename):
                    existing_df = pd.read_excel(filename)
                    combined_df = pd.concat([existing_df, pd.DataFrame(filtered_data)], ignore_index=True)
                else:
                    combined_df = pd.DataFrame(filtered_data)

                # Additional duplicate removal based on multiple columns
                before_dedup = len(combined_df)
                combined_df = combined_df.drop_duplicates(subset=['Company', 'Job Title', 'Location'], keep='first')
                after_dedup = len(combined_df)
                
                if before_dedup > after_dedup:
                    print(f"🔄 Removed {before_dedup - after_dedup} additional duplicates")

                # Save to Excel
                combined_df.to_excel(filename, index=False)
                print(f"✅ {INSTANCE_ID} successfully saved {len(filtered_data)} applications to {filename}")
                
                # Update coordination stats only if we have the lock
                if has_coordination_lock:
                    coordination_client.update_applications_count(len(filtered_data))
                
            except Exception as save_error:
                print(f"❌ Error saving to main file {filename}: {save_error}")
                # Create backup only if main save fails
                fallback_filename = f"job_applications_backup_{INSTANCE_ID}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                pd.DataFrame(filtered_data).to_excel(fallback_filename, index=False)
                print(f"💾 Data saved to backup file: {fallback_filename}")

    except Exception as e:
        print(f"❌ {INSTANCE_ID} critical error in Excel processing: {str(e)}")
        # Final fallback: save with timestamp
        fallback_filename = f"job_applications_backup_{INSTANCE_ID}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        try:
            pd.DataFrame(new_data).to_excel(fallback_filename, index=False)
            print(f"💾 Data saved to emergency backup file: {fallback_filename}")
        except Exception as backup_error:
            print(f"💥 Failed to create backup file: {backup_error}")
    finally:
        # Always release the Excel lock if we had it
        if has_coordination_lock:
            coordination_client.release_excel_lock()

def extract_job_metadata(driver):
    """Extract comprehensive job metadata from job page"""
    metadata = {
        'Job Title': 'Unknown',
        'Company': 'Unknown',
        'Location': 'Unknown',
        'Experience': 'Unknown',
        'Salary': 'Unknown',
        'Job Description': 'Unknown',
        'Skills': 'Unknown',
        'Posted Date': 'Unknown'
    }

    try:
        # Wait for page to load
        WebDriverWait(driver, 10).until(
            EC.any_of(
                EC.presence_of_element_located((By.CSS_SELECTOR, "h1")),
                EC.presence_of_element_located((By.CSS_SELECTOR, ".jd-header")),
                EC.presence_of_element_located((By.CSS_SELECTOR, ".job-header"))
            )
        )
        time.sleep(2)  # Additional wait for dynamic content

        # Job Title - Try multiple selectors (enhanced)
        try:
            title_selectors = [
                # Primary job detail page selectors
                "h1.jd-header-title",
                "h1[data-automation='job-title']", 
                ".jd-header-title",
                "h1.job-title",
                ".job-header h1",
                
                # Additional selectors for different layouts
                ".job-title h1",
                ".jd-header h1",
                "h1.title",
                ".title h1",
                
                # Generic h1 and title selectors
                "h1",
                ".main-title",
                "[class*='title']:not([class*='sub'])",
                
                # Search result fallback (though we should be on job detail page)
                "a.title",
                ".job-tuple h2 a"
            ]
            
            for selector in title_selectors:
                try:
                    title_element = driver.find_element(By.CSS_SELECTOR, selector)
                    title_text = title_element.text.strip()
                    
                    # Validate title - skip if too generic or empty
                    if (title_text and 
                        len(title_text) > 2 and 
                        title_text.lower() not in ['job title', 'title', 'position']):
                        metadata['Job Title'] = title_text
                        print(f"✅ Found job title: {metadata['Job Title']} (using selector: {selector})")
                        break
                except:
                    continue
        except Exception as e:
            print(f"❌ Error extracting job title: {e}")

        # Company Name - Try multiple selectors (enhanced with more patterns)
        try:
            company_selectors = [
                # Primary job detail page selectors
                ".jd-header-comp-name",
                "[data-automation='company-name']",
                ".comp-name",
                ".company-name",
                ".jd-header .company",
                "a.comp-name",
                ".job-header .company",
                
                # Additional selectors for different page layouts
                ".jd-header-comp-name a",
                ".comp-name a",
                "a[title*='jobs']",  # Company links often have 'jobs' in title
                ".company-details .name",
                ".employer-name",
                ".hiring-company",
                
                # Search result page fallback selectors
                "a.comp-name.mw-25",  # Based on your HTML sample
                ".comp-dtls-wrap a.comp-name",
                
                # Generic company text patterns
                ".company",
                "[class*='company']",
                "[class*='comp-name']"
            ]
            
            for selector in company_selectors:
                try:
                    company_element = driver.find_element(By.CSS_SELECTOR, selector)
                    company_text = company_element.text.strip()
                    
                    # Additional validation - skip if text is too generic or empty
                    if (company_text and 
                        len(company_text) > 1 and 
                        company_text.lower() not in ['company', 'employer', 'hiring', 'jobs', 'careers']):
                        metadata['Company'] = company_text
                        print(f"✅ Found company: {metadata['Company']} (using selector: {selector})")
                        break
                except:
                    continue
                    
            # If still not found, try alternative approaches
            if metadata['Company'] == 'Unknown':
                try:
                    # Look for company name in page title or meta tags
                    page_title = driver.title
                    if ' - ' in page_title:
                        # Often format is "Job Title - Company Name"
                        parts = page_title.split(' - ')
                        if len(parts) >= 2:
                            potential_company = parts[-1].strip()
                            if potential_company.lower() not in ['naukri.com', 'naukri', 'jobs']:
                                metadata['Company'] = potential_company
                                print(f"✅ Found company from page title: {metadata['Company']}")
                except Exception as e:
                    print(f"⚠️ Error extracting company from title: {e}")
                    
        except Exception as e:
            print(f"❌ Error extracting company: {e}")

        # Location - Try multiple selectors (enhanced)
        try:
            location_selectors = [
                # Primary job detail page selectors
                ".jd-location",
                "[data-automation='job-location']",
                ".location",
                ".job-location",
                ".jd-header .location",
                
                # Additional selectors for different layouts
                ".job-details .location",
                ".location-details",
                ".work-location",
                ".job-loc",
                
                # Icon-based selectors
                ".ni-job-tuple-icon-srp-location + span",
                ".loc-wrap .locWdth",
                ".exp-loc .location",
                
                # Generic location patterns
                "[class*='location']",
                "[class*='loc']",
                ".address",
                ".city"
            ]
            
            for selector in location_selectors:
                try:
                    location_element = driver.find_element(By.CSS_SELECTOR, selector)
                    location_text = location_element.text.strip()
                    
                    # Validate location - skip if too generic or empty
                    if (location_text and 
                        len(location_text) > 1 and 
                        location_text.lower() not in ['location', 'loc', 'city', 'place']):
                        metadata['Location'] = location_text
                        print(f"✅ Found location: {metadata['Location']} (using selector: {selector})")
                        break
                except:
                    continue
        except Exception as e:
            print(f"❌ Error extracting location: {e}")

        # Experience - Try multiple selectors
        try:
            experience_selectors = [
                ".jd-experience",
                "[data-automation='job-experience']",
                ".experience",
                ".job-experience", 
                ".exp-wrap .expwdth",
                ".ni-job-tuple-icon-srp-experience + span",
                ".job-details .exp",
                "[class*='exp']",
                ".years",
                ".experience-required"
            ]
            
            for selector in experience_selectors:
                try:
                    exp_element = driver.find_element(By.CSS_SELECTOR, selector)
                    exp_text = exp_element.text.strip()
                    
                    if (exp_text and 
                        len(exp_text) > 1 and 
                        exp_text.lower() not in ['experience', 'exp', 'years']):
                        metadata['Experience'] = exp_text
                        print(f"✅ Found experience: {metadata['Experience']} (using selector: {selector})")
                        break
                except:
                    continue
        except Exception as e:
            print(f"❌ Error extracting experience: {e}")

        # Salary - Try multiple selectors
        try:
            salary_selectors = [
                ".jd-salary",
                "[data-automation='job-salary']",
                ".salary",
                ".job-salary",
                ".compensation",
                ".package",
                ".ctc",
                "[class*='salary']",
                "[class*='package']",
                ".pay"
            ]
            
            for selector in salary_selectors:
                try:
                    salary_element = driver.find_element(By.CSS_SELECTOR, selector)
                    salary_text = salary_element.text.strip()
                    
                    if (salary_text and 
                        len(salary_text) > 1 and 
                        salary_text.lower() not in ['salary', 'package', 'ctc', 'compensation']):
                        metadata['Salary'] = salary_text
                        print(f"✅ Found salary: {metadata['Salary']} (using selector: {selector})")
                        break
                except:
                    continue
        except Exception as e:
            print(f"❌ Error extracting salary: {e}")

        # Skills - Try multiple selectors
        try:
            skills_selectors = [
                ".jd-skills",
                "[data-automation='job-skills']",
                ".skills",
                ".job-skills",
                ".tags-gt",
                ".skills-list",
                ".key-skills",
                ".required-skills",
                "[class*='skill']",
                "[class*='tag']"
            ]
            
            skills_found = []
            for selector in skills_selectors:
                try:
                    skills_elements = driver.find_elements(By.CSS_SELECTOR, f"{selector} li, {selector} span, {selector}")
                    for element in skills_elements:
                        skill_text = element.text.strip()
                        if (skill_text and 
                            len(skill_text) > 1 and 
                            skill_text.lower() not in ['skills', 'tags', 'requirements'] and
                            skill_text not in skills_found):
                            skills_found.append(skill_text)
                            
                    if skills_found:
                        metadata['Skills'] = ", ".join(skills_found[:10])  # Limit to first 10 skills
                        print(f"✅ Found skills: {metadata['Skills'][:100]}... (using selector: {selector})")
                        break
                except:
                    continue
        except Exception as e:
            print(f"❌ Error extracting skills: {e}")

        # Job Description - Try multiple selectors
        try:
            description_selectors = [
                ".jd-description",
                "[data-automation='job-description']",
                ".job-description",
                ".job-desc",
                ".description",
                ".jd-content",
                ".job-summary",
                ".job-details-content",
                "[class*='description']",
                "[class*='desc']"
            ]
            
            for selector in description_selectors:
                try:
                    desc_element = driver.find_element(By.CSS_SELECTOR, selector)
                    desc_text = desc_element.text.strip()
                    
                    if (desc_text and 
                        len(desc_text) > 10 and  # Longer validation for descriptions
                        desc_text.lower() not in ['description', 'job description', 'details']):
                        # Truncate description if too long
                        metadata['Job Description'] = desc_text[:500] + "..." if len(desc_text) > 500 else desc_text
                        print(f"✅ Found job description: {len(desc_text)} characters (using selector: {selector})")
                        break
                except:
                    continue
        except Exception as e:
            print(f"❌ Error extracting job description: {e}")

        # Posted Date - Try multiple selectors
        try:
            posted_selectors = [
                ".jd-posted-date",
                "[data-automation='posted-date']",
                ".posted-date",
                ".job-posted",
                ".post-date",
                ".job-post-day",
                ".posted",
                "[class*='posted']",
                "[class*='date']"
            ]
            
            for selector in posted_selectors:
                try:
                    posted_element = driver.find_element(By.CSS_SELECTOR, selector)
                    posted_text = posted_element.text.strip()
                    
                    if (posted_text and 
                        len(posted_text) > 1 and 
                        posted_text.lower() not in ['posted', 'date', 'posted date']):
                        metadata['Posted Date'] = posted_text
                        print(f"✅ Found posted date: {metadata['Posted Date']} (using selector: {selector})")
                        break
                except:
                    continue
        except Exception as e:
            print(f"❌ Error extracting posted date: {e}")

        # Debug: Print extracted metadata
        print(f"📋 EXTRACTED METADATA:")
        print(f"   Job Title: {metadata['Job Title']}")
        print(f"   Company: {metadata['Company']}")
        print(f"   Location: {metadata['Location']}")
        print(f"   Experience: {metadata['Experience']}")
        print(f"   Salary: {metadata['Salary']}")
        print(f"   Skills: {metadata['Skills'][:100]}..." if len(metadata['Skills']) > 100 else f"   Skills: {metadata['Skills']}")
        print(f"   Posted Date: {metadata['Posted Date']}")
        print(f"   Job Description: {len(metadata['Job Description'])} characters")
        
        # Validate that we got at least the essential fields
        essential_fields = ['Job Title', 'Company']
        missing_fields = [field for field in essential_fields if metadata[field] == 'Unknown']
        
        if missing_fields:
            print(f"⚠️ WARNING: Missing essential metadata: {', '.join(missing_fields)}")
            
            # Try one more fallback approach for company name
            if 'Company' in missing_fields:
                try:
                    # Look for any link that might contain company info
                    company_links = driver.find_elements(By.CSS_SELECTOR, "a[href*='company'], a[href*='employer']")
                    for link in company_links:
                        link_text = link.text.strip()
                        if link_text and len(link_text) > 2:
                            metadata['Company'] = link_text
                            print(f"✅ Found company via fallback: {metadata['Company']}")
                            break
                except Exception as e:
                    print(f"⚠️ Fallback company extraction failed: {e}")
        else:
            print(f"✅ All essential metadata extracted successfully!")

    except Exception as e:
        print(f"❌ Error extracting job metadata: {str(e)}")

    return metadata

def chat_with_bot(question):
    """Send chatbot question to API and get the response"""
    try:
        print(f"🔗 {INSTANCE_ID} Sending API request to {API_URL}")
        response = requests.post(API_URL, json={"message": question}, timeout=30)
        print(f"📡 {INSTANCE_ID} API response status: {response.status_code}")
        print(f"📡 {INSTANCE_ID} API response: {response.text}")
        if response.status_code == 200:
            return response.json()["response"]["content"]
        else:
            print(f"❌ {INSTANCE_ID} API returned non-200 status: {response.status_code}")
            return None
    except requests.exceptions.Timeout as e:
        print(f"⏰ {INSTANCE_ID} API request timeout: {e}")
        return None
    except requests.exceptions.ConnectionError as e:
        print(f"🔌 {INSTANCE_ID} API connection error: {e}")
        return None
    except requests.exceptions.RequestException as e:
        print(f"❌ {INSTANCE_ID} API request failed: {e}")
        return None
    except Exception as e:
        print(f"❌ {INSTANCE_ID} Unexpected error in chat_with_bot: {e}")
        return None
    
def extract_phone_number(driver):
    """Extract phone numbers from job description using regex"""
    try:
        jd_container = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "section.styles_job-desc-container__txpYf"))
        )
        jd_text = jd_container.text
        
        # Enhanced phone number regex pattern
        phone_regex = r'\b(?:\+?\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}\b|\b\d{10}\b|\b\d{5}[-.\s]?\d{5}\b'
        numbers_found = re.findall(phone_regex, jd_text)
        
        # Clean and validate numbers
        valid_numbers = []
        for num in numbers_found:
            # Remove non-digit characters
            cleaned_num = re.sub(r'\D', '', num)
            # Check for valid length (9-10 digits)
            if 9 <= len(cleaned_num) <= 10:
                # Format with last 10 digits if longer
                valid_numbers.append(cleaned_num[-10:] if len(cleaned_num) > 10 else cleaned_num)
        
        return ", ".join(valid_numbers) if valid_numbers else "Not Found"
        
    except Exception as e:
        print(f"Error extracting phone number: {str(e)}")
        return "Error"

def setup_driver():
    options = webdriver.ChromeOptions()
    options.add_argument("--start-maximized")
    options.add_argument("--disable-notifications")
    driver = webdriver.Chrome(options=options)
    driver.set_page_load_timeout(PAGE_LOAD_TIMEOUT)
    return driver, WebDriverWait(driver, 20)

def handle_authentication(driver, wait):
    try:
        driver.get("https://www.naukri.com/")
        login_button = wait.until(EC.element_to_be_clickable((By.ID, "login_Layer")))
        login_button.click()

        email_field = wait.until(EC.element_to_be_clickable((By.XPATH, "//input[@type='text']")))
        email_field.send_keys(os.getenv('NAUKRI_EMAIL'))
        
        password_field = driver.find_element(By.XPATH, "//input[@type='password']")
        password_field.send_keys(os.getenv('NAUKRI_PASSWORD'))
        password_field.send_keys(Keys.RETURN)

        wait.until(EC.url_contains("naukri.com"))
        print("Authentication successful")
    except Exception as e:
        print(f"Authentication failed: {str(e)}")
        raise

def configure_search(wait, search_terms=None, locations=None):
    try:
        # Default values if not provided
        if search_terms is None:
            search_terms = "Data analytics,research analyst,Data Analyst,Data Analysis,python developer, Machine Learning, Artificial Intelligence"
        if locations is None:
            locations = "Noida,Gurugram,Pune,Delhi"

        print(f"🔍 Searching for: {search_terms}")
        print(f"📍 Locations: {locations}")

        search_bar = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "div.nI-gNb-sb__main")))
        search_bar.click()

        search_input = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "input.suggestor-input")))
        search_input.clear()
        search_input.send_keys(search_terms)

        # Experience filter
        experience_input = wait.until(EC.presence_of_element_located((By.ID, "experienceDD")))
        experience_input.click()
        experience_options = wait.until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, "ul.dropdown li")))
        experience_options[2].click()

        # Location filter
        location_input = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "div.nI-gNb-sb__locations input.suggestor-input")))
        location_input.clear()
        location_input.send_keys(locations)

        # Execute search
        wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "button.nI-gNb-sb__icon-wrapper"))).click()
        print("Search configured successfully")
    except Exception as e:
        print(f"Search configuration failed: {str(e)}")
        raise

def apply_filters(wait):
    driver = wait._driver  # Get driver from WebDriverWait object
    
    try:
        salary_ranges = ['3-6 Lakhs', '6-10 Lakhs', '10-15 Lakhs']
        for salary in salary_ranges:
            try:
                wait.until(EC.element_to_be_clickable((By.XPATH, f"//span[@title='{salary}']"))).click()
                print(f"Applied salary filter: {salary}")
                time.sleep(1)
            except:
                print(f"Failed to apply {salary} filter")

        # Education filter
        wait.until(EC.element_to_be_clickable((By.ID, "educationViewMoreId"))).click()
        time.sleep(8)
        wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, ".styles_filter-apply-btn__MDAUd"))).click()
        print("Education filters applied")
    except Exception as e:
        print(f"Filter application failed: {str(e)}")
        raise
        
    try:
        # Department filter - Data Science & Analytics only
        print("Applying department filter for Data Science & Analytics")
        
        # First, check if Data Science & Analytics is already selected
        try:
            data_science_checkbox = wait.until(EC.presence_of_element_located(
                (By.CSS_SELECTOR, "input[id*='Data Science'][id*='Analytics'][id*='functionAreaIdGid']")
            ))
            
            # Check if it's already checked
            if not data_science_checkbox.is_selected():
                # Click the label to select it
                data_science_label = wait.until(EC.element_to_be_clickable(
                    (By.CSS_SELECTOR, "label[for*='Data Science'][for*='Analytics'][for*='functionAreaIdGid']")
                ))
                data_science_label.click()
                print("✅ Selected Data Science & Analytics department")
                time.sleep(2)
            else:
                print("✅ Data Science & Analytics department already selected")
            
            # Ensure other departments are unchecked (if any are selected)
            other_departments = [
                "Engineering - Software & QA",
                "IT & Information Security", 
                "Engineering - Hardware & Networks"
            ]
            
            for dept in other_departments:
                try:
                    dept_checkbox = driver.find_element(By.CSS_SELECTOR, f"input[id*='{dept}'][id*='functionAreaIdGid']")
                    if dept_checkbox.is_selected():
                        dept_label = driver.find_element(By.CSS_SELECTOR, f"label[for*='{dept}'][for*='functionAreaIdGid']")
                        dept_label.click()
                        print(f"🚫 Unchecked {dept} department")
                        time.sleep(1)
                except:
                    # Department not found or not selected, continue
                    pass
                    
        except TimeoutException:
            print("⚠️ Could not find Data Science & Analytics department filter")
            
    except Exception as e:
        print(f"Failed to apply department filter: {str(e)}")
        
    try:
        # Freshness
        print("Applying freshness filter")
        freshness_button = wait.until(EC.element_to_be_clickable((By.ID, "filter-freshness")))
        freshness_button.click()
        freshness_options = wait.until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, "ul[data-filter-id='freshness'] li")))
        # Randomly select between 0-2 (inclusive)
        # idx = random.randint(0, 2)
        freshness_options[0].click()
        print(f"Selected freshness option index: {2}")
    except Exception as e:
        print(f"Failed to apply freshness filter: {str(e)}")


def get_new_window(driver, original_handles):
    """Wait for and return new window handle"""
    try:
        WebDriverWait(driver, 10).until(
            lambda d: len(d.window_handles) > len(original_handles)
        )
        return [h for h in driver.window_handles if h not in original_handles][0]
    except TimeoutException:
        print("New window did not open in time")
        return None

def handle_chatbot(driver):
    """Handle chatbot interaction with coordination and multiple safety checks"""
    status = "Application Failed"
    chatbot_used = "No"
    max_interactions = 5
    interactions = 0
    start_time = time.time()
    max_chatbot_time = 300  # minutes maximum for entire chatbot interaction

    # Request chatbot lock for coordination
    if not coordination_client.request_chatbot_lock():
        print(f"⏰ {INSTANCE_ID} could not acquire chatbot lock, skipping")
        return status, chatbot_used

    try:
        WebDriverWait(driver, 15).until(
            EC.presence_of_element_located((By.CLASS_NAME, "chatbot_MessageContainer"))
        )
        chatbot_used = "Yes"
        print(f"🤖 {INSTANCE_ID} chatbot detected - starting conversation")

        while interactions < max_interactions:
            # Check for overall timeout
            if time.time() - start_time > max_chatbot_time:
                print(f"⏰ {INSTANCE_ID} Chatbot interaction timeout ({max_chatbot_time}s), ending")
                break

            try:
                # Check for success message first
                success_elements = driver.find_elements(
                    By.XPATH, '//div[contains(@class, "apply-status-header") and contains(@class, "green")]'
                )
                if success_elements and "successfully applied" in success_elements[0].text.lower():
                    status = "Successfully applied via chatbot"
                    break

                # Get all bot messages
                bot_messages = driver.find_elements(By.CSS_SELECTOR, 'div.botMsg.msg div span')
                if not bot_messages:
                    print(f"🤖 {INSTANCE_ID} No bot messages found, ending conversation")
                    break

                last_question = bot_messages[-1].text.strip()
                if not last_question:
                    print(f"🤖 {INSTANCE_ID} Empty question, ending conversation")
                    break

                print(f"Chatbot question ({interactions+1}/{max_interactions}): {last_question}")

                # Handle different input types (radio buttons, checkboxes, text input)
                input_handled = False

                # First, try to handle radio buttons
                try:
                    radio_container = WebDriverWait(driver, 3).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, ".singleselect-radiobutton"))
                    )

                    radio_buttons = driver.find_elements(By.CSS_SELECTOR, ".ssrc__radio-btn-container input[type='radio']")
                    if radio_buttons:
                        print(f"🎯 {INSTANCE_ID} found {len(radio_buttons)} radio button options")

                        # Smart selection based on question context
                        selected_radio = None
                        question_lower = last_question.lower()

                        # Location-based questions
                        if any(city in question_lower for city in ['pune', 'delhi', 'mumbai', 'bangalore', 'hyderabad', 'chennai', 'kolkata']):
                            # For location questions, prefer "Yes" if it matches user's location
                            for radio in radio_buttons:
                                value = radio.get_attribute('value')
                                if value and value.lower() == 'yes':
                                    selected_radio = radio
                                    break

                        # Experience-related questions
                        elif any(word in question_lower for word in ['experience', 'years', 'worked']):
                            # For experience questions, select appropriate range or "Yes"
                            for radio in radio_buttons:
                                value = radio.get_attribute('value')
                                if value and ('yes' in value.lower() or '3' in value or '2-5' in value):
                                    selected_radio = radio
                                    break

                        # Notice period questions
                        elif "notice period" in question_lower:
                            for radio in radio_buttons:
                                value = radio.get_attribute('value')
                                if value and ("1 month" in value.lower() or "30 days" in value.lower() or "15 days" in value.lower()):
                                    selected_radio = radio
                                    break

                        # Default: select first available option (usually "Yes")
                        if not selected_radio:
                            selected_radio = radio_buttons[0]

                        # Click the selected radio button
                        driver.execute_script("arguments[0].click();", selected_radio)
                        selected_value = selected_radio.get_attribute('value')
                        print(f"✅ {INSTANCE_ID} selected radio button: {selected_value}")
                        time.sleep(2)  # Wait for any dynamic updates

                except (TimeoutException, NoSuchElementException):
                    print(f"🔍 {INSTANCE_ID} No radio buttons found, trying checkboxes...")

                # If no radio buttons, try checkboxes
                if not input_handled:
                    try:
                        checkbox_container = WebDriverWait(driver, 3).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, ".multiselectcheckboxes"))
                        )

                        checkboxes = driver.find_elements(By.CSS_SELECTOR, ".mcc__checkbox")
                        if checkboxes:
                            print(f"☑️ {INSTANCE_ID} found {len(checkboxes)} checkbox options")

                            # Smart selection for checkboxes based on question context
                            question_lower = last_question.lower()
                            selected_checkboxes = []

                            # Experience range questions
                            if any(word in question_lower for word in ['experience', 'years']):
                                # Select appropriate experience range (2-5 years typically)
                                for checkbox in checkboxes:
                                    value = checkbox.get_attribute('value')
                                    if value and ('2-5' in value or '3-5' in value):
                                        selected_checkboxes.append(checkbox)
                                        break

                            # If no smart selection, select first checkbox
                            if not selected_checkboxes and checkboxes:
                                selected_checkboxes.append(checkboxes[0])

                            # Click selected checkboxes
                            for checkbox in selected_checkboxes:
                                driver.execute_script("arguments[0].click();", checkbox)
                                value = checkbox.get_attribute('value')
                                print(f"☑️ {INSTANCE_ID} selected checkbox: {value}")

                            input_handled = True
                            time.sleep(2)  # Wait for any dynamic updates

                    except (TimeoutException, NoSuchElementException):
                        print(f"🔍 {INSTANCE_ID} No checkboxes found, trying text input...")

                # After handling radio buttons or checkboxes, look for submit button
                if input_handled:
                    try:
                        # Look for submit/save button (enhanced with more selectors based on actual HTML)
                        submit_selectors = [
                            # Most specific selectors for your exact HTML structure
                            ".sendMsgbtn_container .sendMsg",  # Your exact structure
                            ".send .sendMsg",  # Alternative path
                            "div.sendMsg[tabindex='0']",  # With tabindex attribute

                            # Div elements that act as buttons (based on your HTML)
                            "div.sendMsg",
                            ".sendMsg",
                            "#sendMsg__upeue7cz8InputBox .sendMsg",

                            # Button elements
                            "button[type='submit']",
                            "button.savesrc__button",
                            "button[class*='save']",
                            "button[class*='submit']",
                            "button.sendMsg",
                            
                            # Generic containers with clickable elements
                            ".chatbot_SendMessage button",
                            ".chatbot_SendMessage .sendMsg",
                            ".footerWrapper button",
                            ".footerWrapper .sendMsg",
                            
                            # Additional save/submit patterns
                            "[class*='save'][tabindex]",
                            "[class*='submit'][tabindex]",
                            "div[tabindex='0'][class*='send']",
                            
                            # Fallback selectors
                            "button[class*='send']",
                            "div[class*='send'][tabindex]"
                        ]

                        submit_button = None
                        successful_selector = None
                        for selector in submit_selectors:
                            try:
                                submit_button = WebDriverWait(driver, 2).until(
                                    EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                                )
                                successful_selector = selector
                                print(f"🎯 {INSTANCE_ID} Found submit button with selector: {selector}")
                                break
                            except:
                                continue

                        if submit_button:
                            try:
                                # Try multiple click methods for better reliability
                                print(f"🖱️ {INSTANCE_ID} Attempting to click submit button...")
                                print(f"🔍 {INSTANCE_ID} Button text: '{submit_button.text}', tag: {submit_button.tag_name}")

                                # Method 1: JavaScript click (most reliable for div elements)
                                driver.execute_script("arguments[0].click();", submit_button)
                                print(f"✅ {INSTANCE_ID} Clicked submit button using JavaScript (selector: {successful_selector})")

                            except Exception as click_error:
                                try:
                                    # Method 2: Regular click as fallback
                                    submit_button.click()
                                    print(f"✅ {INSTANCE_ID} Clicked submit button using regular click")
                                except Exception as fallback_error:
                                    print(f"❌ {INSTANCE_ID} Failed to click submit button: JS={click_error}, Regular={fallback_error}")
                        else:
                            print(f"⚠️ {INSTANCE_ID} No submit button found, selection might auto-submit")
                            print(f"🔍 {INSTANCE_ID} Available elements on page:")
                            try:
                                # Debug: Show available clickable elements
                                clickable_elements = driver.find_elements(By.CSS_SELECTOR, 
                                    "button, div[tabindex], [class*='send'], [class*='save'], [class*='submit']")
                                for i, elem in enumerate(clickable_elements[:5]):  # Show first 5
                                    try:
                                        tag = elem.tag_name
                                        classes = elem.get_attribute('class') or 'no-class'
                                        text = elem.text.strip()[:20] or 'no-text'
                                        print(f"     {i+1}. {tag}.{classes} - '{text}'")
                                    except:
                                        pass
                            except Exception as debug_error:
                                print(f"     Debug failed: {debug_error}")

                        time.sleep(3)  # Wait for next question or completion

                    except Exception as e:
                        print(f"⚠️ {INSTANCE_ID} Submit button handling failed: {e}")
                        time.sleep(2)

                # Fallback to text input if no radio buttons or checkboxes handled
                elif not input_handled:
                    # Fallback to text input
                    try:
                        input_field = WebDriverWait(driver, 5).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, "#userInput__f9ndj8bujInputBox, div[contenteditable='true'].textArea"))
                        )
                        print(f"🤖 {INSTANCE_ID} Calling API for question: {last_question}")
                        answer = chat_with_bot(last_question)
                        if not answer:
                            print(f"❌ {INSTANCE_ID} Empty response from chatbot API, skipping")
                            break

                        input_field.clear()
                        input_field.send_keys(answer)
                        print(f"✅ {INSTANCE_ID} Provided text input: {answer}")

                        # Click send button with enhanced selectors
                        send_button_selectors = [
                            ".sendMsg",
                            "div.sendMsg",
                            "#sendMsg__upeue7cz8InputBox .sendMsg",
                            ".send .sendMsg",
                            ".sendMsgbtn_container .sendMsg",
                            "button.sendMsg",
                            ".chatbot_SendMessage .sendMsg",
                            ".footerWrapper .sendMsg",
                            "div[tabindex='0'][class*='send']"
                        ]
                        
                        send_button = None
                        successful_selector = None
                        for selector in send_button_selectors:
                            try:
                                send_button = WebDriverWait(driver, 2).until(
                                    EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                                )
                                successful_selector = selector
                                print(f"🎯 {INSTANCE_ID} Found send button with selector: {selector}")
                                break
                            except:
                                continue
                        
                        if send_button:
                            try:
                                # Try JavaScript click first for div elements
                                driver.execute_script("arguments[0].click();", send_button)
                                print(f"📤 {INSTANCE_ID} Sent chatbot response using JavaScript (selector: {successful_selector})")
                            except Exception as click_error:
                                try:
                                    send_button.click()
                                    print(f"📤 {INSTANCE_ID} Sent chatbot response using regular click")
                                except Exception as fallback_error:
                                    print(f"❌ {INSTANCE_ID} Failed to click send button: JS={click_error}, Regular={fallback_error}")
                                    break
                        else:
                            print(f"❌ {INSTANCE_ID} No send button found")
                            break

                    except (TimeoutException, NoSuchElementException) as e:
                        print(f"❌ {INSTANCE_ID} No recognizable input method found: {e}")
                        break
                    except Exception as e:
                        print(f"❌ {INSTANCE_ID} Text input failed: {e}")
                        break

                interactions += 1
                time.sleep(2)  # Allow chatbot processing

            except Exception as e:
                print(f"Chatbot interaction error: {str(e)}")
                interactions += 1
                break

    except TimeoutException:
        print(f"⏰ {INSTANCE_ID} no chatbot detected within timeout")
    except Exception as e:
        print(f"❌ {INSTANCE_ID} chatbot handling failed: {str(e)}")
    finally:
        # Always release the chatbot lock
        coordination_client.release_chatbot_lock()

    return status, chatbot_used

def comprehensive_success_check(driver, chatbot_used="No"):
    """
    Comprehensive and robust success detection with multiple verification methods
    Returns: (status, is_successful)
    """
    print(f"🔍 {INSTANCE_ID} performing comprehensive success verification...")
    
    # Wait for page to stabilize after application
    time.sleep(3)
    
    # Multiple success indicators to check
    success_indicators = [
        # Primary success messages
        ('xpath', '//div[contains(@class, "apply-status-header") and contains(@class, "green")]'),
        ('xpath', '//div[contains(text(), "successfully applied") or contains(text(), "Successfully Applied")]'),
        ('xpath', '//div[contains(text(), "Application submitted") or contains(text(), "Application Submitted")]'),
        
        # Alternative success patterns
        ('css', '.success-message, .apply-success, .application-success'),
        ('css', '[class*="success"][class*="apply"], [class*="applied"][class*="success"]'),
        
        # Thank you messages
        ('xpath', '//div[contains(text(), "Thank you") and contains(text(), "application")]'),
        ('xpath', '//div[contains(text(), "Application received") or contains(text(), "Application Received")]'),
        
        # Confirmation messages
        ('css', '.confirmation-message, .apply-confirmation'),
        ('xpath', '//div[contains(@class, "confirmation") and contains(text(), "appli")]'),
    ]
    
    # Check for success indicators
    for selector_type, selector in success_indicators:
        try:
            if selector_type == 'xpath':
                elements = driver.find_elements(By.XPATH, selector)
            else:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
            
            for element in elements:
                element_text = element.text.lower()
                success_keywords = [
                    'successfully applied', 'application submitted', 'application received',
                    'thank you for applying', 'applied successfully', 'application confirmed',
                    'application sent', 'we have received your application'
                ]
                
                if any(keyword in element_text for keyword in success_keywords):
                    status = f"Successfully applied via {'chatbot' if chatbot_used == 'Yes' else 'direct'}"
                    print(f"✅ {INSTANCE_ID} SUCCESS DETECTED: {element_text[:100]}...")
                    return status, True
                    
        except Exception as e:
            continue
    
    # No clear success detected
    print(f"❓ {INSTANCE_ID} No clear success indication found")
    return "Application Status Unclear", False

def apply_with_retry(driver, job_url, job_metadata, current_page, max_retries=1):
    """
    Apply to job with retry mechanism if application fails
    Returns: (record, job_processed_successfully)
    """
    print(f"🎯 {INSTANCE_ID} Starting application process with retry mechanism")
    
    # Check if company is blacklisted before starting retry attempts
    company_name = job_metadata.get('Company', 'Unknown')
    if company_name != 'Unknown' and coordination_client.real_time_blacklist_check(company_name):
        print(f"🚫 {INSTANCE_ID} Skipping retry mechanism - {company_name} is blacklisted")
        return {
            "Job Link": job_url,
            "Status": "Skipped - Company Blacklisted",
            "Chatbot Used": "No",
            "Phone Number": "Not Checked",
            "Page": current_page,
            "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "Retry Attempt": 0,
            **job_metadata
        }, False  # Not considered successful since we skipped it
    
    for attempt in range(max_retries + 1):
        if attempt > 0:
            print(f"🔄 {INSTANCE_ID} RETRY ATTEMPT {attempt}/{max_retries}")
            # Use back button to return to job application page instead of refresh
            try:
                print(f"🔙 {INSTANCE_ID} Navigating back to job application page...")
                driver.back()
                time.sleep(2)
                
                # Wait for the page to load completely
                WebDriverWait(driver, 15).until(
                    lambda d: d.execute_script("return document.readyState") == "complete"
                )
                time.sleep(1)
                
                # Verify we're back on the job page
                try:
                    WebDriverWait(driver, 10).until(
                        EC.any_of(
                            EC.presence_of_element_located((By.ID, "apply-button")),
                            EC.presence_of_element_located((By.CSS_SELECTOR, ".jd-header")),
                            EC.presence_of_element_located((By.CSS_SELECTOR, ".job-header")),
                            EC.presence_of_element_located((By.ID, "already-applied")),
                            EC.presence_of_element_located((By.ID, "company-site-button"))
                        )
                    )
                    print(f"✅ {INSTANCE_ID} Successfully returned to job application page")
                except TimeoutException:
                    print(f"⚠️ {INSTANCE_ID} May not be on correct page after back navigation")
                    
            except Exception as e:
                print(f"⚠️ {INSTANCE_ID} Back navigation failed: {e}, trying refresh as fallback")
                driver.refresh()
                time.sleep(3)
        
        try:
            status = "Application Failed"
            chatbot_used = "No"
            
            # Check for already applied status first
            try:
                already_applied = WebDriverWait(driver, 5).until(
                    EC.visibility_of_element_located((By.ID, "already-applied"))
                )
                return {
                    "Job Link": job_url,
                    "Status": "Already Applied",
                    "Chatbot Used": "No",
                    "Phone Number": "Not Checked",
                    "Page": current_page,
                    "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "Retry Attempt": attempt + 1,
                    **job_metadata
                }, True
            except TimeoutException:
                pass

            # Check for company site redirect
            try:
                company_site_btn = WebDriverWait(driver, 3).until(
                    EC.visibility_of_element_located((By.ID, "company-site-button"))
                )
                return {
                    "Job Link": job_url,
                    "Status": "External Application Required",
                    "Chatbot Used": "No",
                    "Phone Number": "Not Checked",
                    "Page": current_page,
                    "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "Retry Attempt": attempt + 1,
                    **job_metadata
                }, True
            except TimeoutException:
                pass

            # Click apply button
            try:
                apply_btn = WebDriverWait(driver, 15).until(
                    EC.element_to_be_clickable((By.ID, "apply-button"))
                )
                apply_btn.click()
                print(f"🖱️ {INSTANCE_ID} Clicked apply button (Attempt {attempt + 1})")
                time.sleep(3)
            except TimeoutException:
                if attempt < max_retries:
                    continue
                status = "Apply button not found"
                break

            # Handle chatbot interaction
            status, chatbot_used = handle_chatbot(driver)
            
            # Comprehensive success verification
            final_status, is_successful = comprehensive_success_check(driver, chatbot_used)
            
            if is_successful:
                print(f"🎉 {INSTANCE_ID} APPLICATION SUCCESSFUL on attempt {attempt + 1}")
                return {
                    "Job Link": job_url,
                    "Status": final_status,
                    "Chatbot Used": chatbot_used,
                    "Phone Number": "Not Checked",
                    "Page": current_page,
                    "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "Retry Attempt": attempt + 1,
                    **job_metadata
                }, True
            else:
                if attempt < max_retries:
                    print(f"🔄 {INSTANCE_ID} Will retry application...")
                    continue
                status = final_status
                break
                    
        except Exception as e:
            if attempt < max_retries:
                continue
            status = f"Application Error: {str(e)}"
            break
    
    # All retries exhausted
    print(f"💔 {INSTANCE_ID} Application failed after {max_retries + 1} attempts")
    return {
        "Job Link": job_url,
        "Status": status,
        "Chatbot Used": chatbot_used,
        "Phone Number": "Not Checked",
        "Page": current_page,
        "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "Retry Attempt": max_retries + 1,
        **job_metadata
    }, False

def process_job_page(driver, wait, current_page):

    try:
        # Initial job list retrieval with retries
        jobs = wait.until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, "div.srp-jobtuple-wrapper")))
        print(f"\nFound {len(jobs)} jobs on page {current_page}")

        for idx in range(1, len(jobs)+1):
            job_processed = False
            for retry in range(MAX_RETRIES + 1):
                try:
                    if retry > 0:
                        print(f"Retry {retry}/{MAX_RETRIES} for job {idx}")
                        jobs = wait.until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, "div.srp-jobtuple-wrapper")))
                        if idx-1 >= len(jobs):
                            print(f"Job index {idx} out of bounds after refresh, skipping")
                            break
                        job = jobs[idx-1]

                    # Refresh job reference each iteration
                    jobs = wait.until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, "div.srp-jobtuple-wrapper")))
                    job = jobs[idx-1] if idx-1 < len(jobs) else None
                    if not job:
                        print(f"Job index {idx} no longer exists, skipping")
                        break

                    link = WebDriverWait(job, 10).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "a.title")))
                    job_url = link.get_attribute('href').strip()
                    if not job_url:
                        raise ValueError("Empty job URL")

                    print(f"\nProcessing job {idx}/{len(jobs)}: {job_url[:60]}...")

                    # Window management
                    original_windows = driver.window_handles
                    driver.execute_script("window.open(arguments[0]);", job_url)
                    new_window = get_new_window(driver, original_windows)

                    if not new_window:
                        raise WebDriverException("Failed to open new window")

                    driver.switch_to.window(new_window)
                    status = "Application Failed"
                    chatbot_used = "No"
                    phone_number = "Not Checked"

                    try:
                        # Extract job metadata first
                        job_metadata = extract_job_metadata(driver)
                        print(f"📋 Job: {job_metadata['Job Title']} at {job_metadata['Company']}")

                        # Real-time blacklist check with smart company tracking
                        company_name = job_metadata.get('Company', 'Unknown')
                        if company_name != 'Unknown':
                            # Use smart tracking that includes real-time sync and metadata collection
                            new_count, was_blacklisted, should_skip = coordination_client.smart_company_tracking(company_name, job_metadata)
                            
                            if should_skip:
                                print(f"🚫 Skipping {company_name} - Company is blacklisted (real-time check)")
                                driver.close()
                                driver.switch_to.window(original_windows[0])
                                continue

                        # Retry mechanism for application
                        application_successful = False
                        final_status = "Application Failed"
                        final_chatbot_used = "No"
                        
                        for retry_attempt in range(APPLICATION_RETRY_ATTEMPTS + 1):
                            if retry_attempt > 0:
                                print(f"🔄 {INSTANCE_ID} RETRY ATTEMPT {retry_attempt}/{APPLICATION_RETRY_ATTEMPTS}")
                                # Use back button to return to job application page instead of refresh
                                try:
                                    print(f"🔙 {INSTANCE_ID} Navigating back to job application page...")
                                    driver.back()
                                    time.sleep(2)
                                    
                                    # Wait for the page to load completely
                                    WebDriverWait(driver, 15).until(
                                        lambda d: d.execute_script("return document.readyState") == "complete"
                                    )
                                    time.sleep(1)
                                    
                                    # Verify we're back on the job page by checking for apply button or job content
                                    try:
                                        WebDriverWait(driver, 10).until(
                                            EC.any_of(
                                                EC.presence_of_element_located((By.ID, "apply-button")),
                                                EC.presence_of_element_located((By.CSS_SELECTOR, ".jd-header")),
                                                EC.presence_of_element_located((By.CSS_SELECTOR, ".job-header")),
                                                EC.presence_of_element_located((By.ID, "already-applied")),
                                                EC.presence_of_element_located((By.ID, "company-site-button"))
                                            )
                                        )
                                        print(f"✅ {INSTANCE_ID} Successfully returned to job application page")
                                    except TimeoutException:
                                        print(f"⚠️ {INSTANCE_ID} May not be on correct page after back navigation")
                                        
                                except Exception as e:
                                    print(f"⚠️ {INSTANCE_ID} Back navigation failed: {e}, trying refresh as fallback")
                                    driver.refresh()
                                    time.sleep(3)
                            
                            try:
                                # Check for already applied status first
                                try:
                                    already_applied = WebDriverWait(driver, 5).until(
                                        EC.visibility_of_element_located((By.ID, "already-applied"))
                                    )
                                    final_status = "Already Applied"
                                    application_successful = True
                                    break
                                except TimeoutException:
                                    pass

                                # Check for company site redirect
                                try:
                                    company_site_btn = WebDriverWait(driver, 3).until(
                                        EC.visibility_of_element_located((By.ID, "company-site-button"))
                                    )
                                    final_status = "External Application Required"
                                    application_successful = True
                                    break
                                except TimeoutException:
                                    pass

                                # Click apply button
                                try:
                                    apply_btn = WebDriverWait(driver, 15).until(
                                        EC.element_to_be_clickable((By.ID, "apply-button"))
                                    )
                                    apply_btn.click()
                                    print(f"🖱️ {INSTANCE_ID} Clicked apply button (Attempt {retry_attempt + 1})")
                                    time.sleep(3)
                                except TimeoutException:
                                    if retry_attempt < APPLICATION_RETRY_ATTEMPTS:
                                        continue
                                    final_status = "Apply button not found"
                                    break

                                # Handle chatbot interaction
                                final_status, final_chatbot_used = handle_chatbot(driver)
                                
                                # Comprehensive success verification
                                verified_status, is_successful = comprehensive_success_check(driver, final_chatbot_used)
                                
                                if is_successful:
                                    print(f"🎉 {INSTANCE_ID} APPLICATION SUCCESSFUL on attempt {retry_attempt + 1}")
                                    final_status = verified_status
                                    application_successful = True
                                    break
                                else:
                                    if retry_attempt < APPLICATION_RETRY_ATTEMPTS:
                                        print(f"🔄 {INSTANCE_ID} Will retry application...")
                                        continue
                                    final_status = verified_status
                                    break
                                        
                            except Exception as e:
                                if retry_attempt < APPLICATION_RETRY_ATTEMPTS:
                                    continue
                                final_status = f"Application Error: {str(e)}"
                                break
                        
                        # Create record with retry information
                        record = {
                            "Job Link": job_url,
                            "Status": final_status,
                            "Chatbot Used": final_chatbot_used,
                            "Phone Number": "Not Checked",
                            "Page": current_page,
                            "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                            "Retry Attempt": min(retry_attempt + 1, APPLICATION_RETRY_ATTEMPTS + 1),
                            **job_metadata
                        }
                        applications.append(record)
                        
                        if application_successful:
                            print(f"✅ {INSTANCE_ID} Job {idx} processed successfully")
                            # Enhanced company tracking is already done in smart_company_tracking above
                            # Just log the success
                            company_name = job_metadata.get('Company', 'Unknown')
                            if company_name != 'Unknown':
                                # Get current company metadata for logging
                                metadata = coordination_client.get_company_metadata(company_name)
                                total_jobs = metadata.get('total_jobs_found', 1)
                                locations = metadata.get('locations', [])
                                print(f"� {company_name} stats: {total_jobs} jobs found in {len(locations)} locations")
                        else:
                            print(f"❌ {INSTANCE_ID} Job {idx} failed after {APPLICATION_RETRY_ATTEMPTS + 1} attempts")

                    except Exception as e:
                        print(f"❌ {INSTANCE_ID} Critical error processing job {idx}: {str(e)}")
                        # Create minimal record for failed job
                        failed_record = {
                            "Job Link": job_url if 'job_url' in locals() else "Unknown",
                            "Status": f"Critical Error: {str(e)}",
                            "Chatbot Used": "No",
                            "Phone Number": "Not Checked",
                            "Page": current_page,
                            "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                            "Retry Attempt": 1,
                            "Job Title": "Unknown",
                            "Company": "Unknown",
                            "Location": "Unknown",
                            "Experience": "Unknown",
                            "Salary": "Unknown",
                            "Job Description": "Unknown",
                            "Skills": "Unknown",
                            "Posted Date": "Unknown"
                        }
                        applications.append(failed_record)

                    # Cleanup windows
                    try:
                        driver.close()
                        driver.switch_to.window(original_windows[0])
                    except Exception as e:
                        print(f"⚠️ {INSTANCE_ID} Window cleanup error: {e}")
                    
                    job_processed = True
                    break

                except StaleElementReferenceException:
                    print(f"🔄 {INSTANCE_ID} Stale element reference while processing job {idx}")
                    time.sleep(2)
                except Exception as e:
                    print(f"❌ {INSTANCE_ID} Error processing job {idx}: {str(e)}")
                    if retry == MAX_RETRIES:
                        print(f"💔 {INSTANCE_ID} Max retries reached for job {idx}")
                    time.sleep(1)

            if not job_processed:
                print(f"💔 {INSTANCE_ID} Failed to process job {idx} after {MAX_RETRIES} retries")
                # Create minimal record for failed job
                failed_record = {
                    "Job Link": job_url if 'job_url' in locals() else "Unknown",
                    "Status": "Failed after retries",
                    "Chatbot Used": "No",
                    "Phone Number": "Not Checked",
                    "Page": current_page,
                    "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "Retry Attempt": MAX_RETRIES,
                    "Job Title": "Unknown",
                    "Company": "Unknown",
                    "Location": "Unknown",
                    "Experience": "Unknown",
                    "Salary": "Unknown",
                    "Job Description": "Unknown",
                    "Skills": "Unknown",
                    "Posted Date": "Unknown"
                }
                applications.append(failed_record)

        return True

    except Exception as e:
        print(f"💥 {INSTANCE_ID} Critical error processing page {current_page}: {str(e)}")
        return False

def comprehensive_success_check(driver, chatbot_used="No"):
    """
    Comprehensive and robust success detection with multiple verification methods
    Returns: (status, is_successful)
    """
    print(f"🔍 {INSTANCE_ID} performing comprehensive success verification...")
    
    # Wait for page to stabilize after application
    time.sleep(3)
    
    # Multiple success indicators to check
    success_indicators = [
        # Primary success messages
        ('xpath', '//div[contains(@class, "apply-status-header") and contains(@class, "green")]'),
        ('xpath', '//div[contains(text(), "successfully applied") or contains(text(), "Successfully Applied")]'),
        ('xpath', '//div[contains(text(), "Application submitted") or contains(text(), "Application Submitted")]'),
        
        # Alternative success patterns
        ('css', '.success-message, .apply-success, .application-success'),
        ('css', '[class*="success"][class*="apply"], [class*="applied"][class*="success"]'),
        
        # Thank you messages
        ('xpath', '//div[contains(text(), "Thank you") and contains(text(), "application")]'),
        ('xpath', '//div[contains(text(), "Application received") or contains(text(), "Application Received")]'),
        
        # Confirmation messages
        ('css', '.confirmation-message, .apply-confirmation'),
        ('xpath', '//div[contains(@class, "confirmation") and contains(text(), "appli")]'),
    ]
    
    # Check for success indicators
    for selector_type, selector in success_indicators:
        try:
            if selector_type == 'xpath':
                elements = driver.find_elements(By.XPATH, selector)
            else:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
            
            for element in elements:
                element_text = element.text.lower()
                success_keywords = [
                    'successfully applied', 'application submitted', 'application received',
                    'thank you for applying', 'applied successfully', 'application confirmed',
                    'application sent', 'we have received your application'
                ]
                
                if any(keyword in element_text for keyword in success_keywords):
                    status = f"Successfully applied via {'chatbot' if chatbot_used == 'Yes' else 'direct'}"
                    print(f"✅ {INSTANCE_ID} SUCCESS DETECTED: {element_text[:100]}...")
                    return status, True
                    
        except Exception as e:
            continue
    
    # Check for "Already Applied" status (also considered successful)
    try:
        already_applied_selectors = [
            (By.ID, "already-applied"),
            (By.XPATH, '//div[contains(text(), "already applied") or contains(text(), "Already Applied")]'),
            (By.CSS_SELECTOR, '.already-applied, [class*="already"][class*="applied"]')
        ]
        
        for by_type, selector in already_applied_selectors:
            elements = driver.find_elements(by_type, selector)
            if elements and any(elem.is_displayed() for elem in elements):
                print(f"✅ {INSTANCE_ID} ALREADY APPLIED detected")
                return "Already Applied", True
                
    except Exception as e:
        pass
    
    # Check for failure indicators
    failure_indicators = [
        ('xpath', '//div[contains(text(), "failed") or contains(text(), "error") or contains(text(), "unable")]'),
        ('css', '.error-message, .apply-error, .application-error'),
        ('xpath', '//div[contains(@class, "error") and contains(text(), "appli")]'),
    ]
    
    for selector_type, selector in failure_indicators:
        try:
            if selector_type == 'xpath':
                elements = driver.find_elements(By.XPATH, selector)
            else:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
            
            for element in elements:
                if element.is_displayed():
                    error_text = element.text[:100]
                    print(f"❌ {INSTANCE_ID} FAILURE DETECTED: {error_text}...")
                    return f"Application Failed: {error_text}", False
                    
        except Exception as e:
            continue
    
    # Check if still on application form (indicates incomplete application)
    try:
        apply_button = driver.find_elements(By.ID, "apply-button")
        chatbot_containers = driver.find_elements(By.CLASS_NAME, "chatbot_MessageContainer")
        
        if apply_button and apply_button[0].is_displayed():
            print(f"⚠️ {INSTANCE_ID} Still on application form - application incomplete")
            return "Application Incomplete - Still on form", False
        
        if chatbot_containers and chatbot_containers[0].is_displayed():
            # Check if chatbot is still waiting for input
            input_fields = driver.find_elements(By.CSS_SELECTOR, 
                'div[contenteditable="true"].textArea, input[type="text"]')
            radio_buttons = driver.find_elements(By.CSS_SELECTOR, 
                '.ssrc__radio-btn-container input[type="radio"]')
            
            if input_fields or radio_buttons:
                print(f"⚠️ {INSTANCE_ID} Chatbot still waiting for input - application incomplete")
                return "Application Incomplete - Chatbot pending", False
                
    except Exception as e:
        pass
    
    # If no clear success or failure found, check page URL and title
    try:
        current_url = driver.current_url.lower()
        page_title = driver.title.lower()
        
        if 'success' in current_url or 'applied' in current_url:
            print(f"✅ {INSTANCE_ID} SUCCESS indicated by URL: {current_url}")
            return "Successfully applied - URL indicates success", True
        
        if 'success' in page_title or 'applied' in page_title:
            print(f"✅ {INSTANCE_ID} SUCCESS indicated by page title: {page_title}")
            return "Successfully applied - Title indicates success", True
            
    except Exception as e:
        pass
    
    # Final check - look for any positive confirmation text on page
    try:
        page_text = driver.find_element(By.TAG_NAME, "body").text.lower()
        positive_phrases = [
            'application submitted', 'successfully applied', 'application received',
            'thank you for applying', 'we will contact you', 'application confirmed'
        ]
        
        for phrase in positive_phrases:
            if phrase in page_text:
                print(f"✅ {INSTANCE_ID} SUCCESS detected in page text")
                return f"Successfully applied - {phrase} found", True
                
    except Exception as e:
        pass
    
    # No clear success detected
    print(f"❓ {INSTANCE_ID} No clear success indication found")
    return "Application Status Unclear", False

def apply_with_retry(driver, job_url, job_metadata, current_page, max_retries=1):
    """
    Apply to job with retry mechanism if application fails
    Returns: (record, job_processed_successfully)
    """
    print(f"🎯 {INSTANCE_ID} Starting application process with retry mechanism")
    
    # Check if company is blacklisted before starting retry attempts
    company_name = job_metadata.get('Company', 'Unknown')
    if company_name != 'Unknown' and coordination_client.real_time_blacklist_check(company_name):
        print(f"🚫 {INSTANCE_ID} Skipping retry mechanism - {company_name} is blacklisted")
        return {
            "Job Link": job_url,
            "Status": "Skipped - Company Blacklisted",
            "Chatbot Used": "No",
            "Phone Number": "Not Checked",
            "Page": current_page,
            "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "Retry Attempt": 0,
            **job_metadata
        }, False  # Not considered successful since we skipped it
    
    for attempt in range(max_retries + 1):
        if attempt > 0:
            print(f"🔄 {INSTANCE_ID} RETRY ATTEMPT {attempt}/{max_retries}")
            # Use back button to return to job application page instead of refresh
            try:
                print(f"🔙 {INSTANCE_ID} Navigating back to job application page...")
                driver.back()
                time.sleep(2)
                
                # Wait for the page to load completely
                WebDriverWait(driver, 15).until(
                    lambda d: d.execute_script("return document.readyState") == "complete"
                )
                time.sleep(1)
                
                # Verify we're back on the job page
                try:
                    WebDriverWait(driver, 10).until(
                        EC.any_of(
                            EC.presence_of_element_located((By.ID, "apply-button")),
                            EC.presence_of_element_located((By.CSS_SELECTOR, ".jd-header")),
                            EC.presence_of_element_located((By.CSS_SELECTOR, ".job-header")),
                            EC.presence_of_element_located((By.ID, "already-applied")),
                            EC.presence_of_element_located((By.ID, "company-site-button"))
                        )
                    )
                    print(f"✅ {INSTANCE_ID} Successfully returned to job application page")
                except TimeoutException:
                    print(f"⚠️ {INSTANCE_ID} May not be on correct page after back navigation")
                    
            except Exception as e:
                print(f"⚠️ {INSTANCE_ID} Back navigation failed: {e}, trying refresh as fallback")
                driver.refresh()
                time.sleep(3)
            
            # Wait for page to load (keeping original logic as additional safety)
            try:
                WebDriverWait(driver, 15).until(
                    lambda d: d.execute_script("return document.readyState") == "complete"
                )
            except TimeoutException:
                print(f"⏰ {INSTANCE_ID} Page reload timeout on retry {attempt}")
                continue
        
        try:
            status = "Application Failed"
            chatbot_used = "No"
            phone_number = "Not Checked"
            
            print(f"📋 {INSTANCE_ID} Job: {job_metadata['Job Title']} at {job_metadata['Company']} (Attempt {attempt + 1})")

            # Check for already applied status first
            try:
                already_applied = WebDriverWait(driver, 5).until(
                    EC.visibility_of_element_located((By.ID, "already-applied"))
                )
                status = "Already Applied"
                print(f"✅ {INSTANCE_ID} Already applied detected")
                
                # Still extract phone if possible
                try:
                    phone_number = extract_phone_number(driver)
                    print(f"📞 {INSTANCE_ID} Found phone numbers: {phone_number}")
                except Exception as e:
                    print(f"📞 {INSTANCE_ID} Phone extraction failed: {str(e)}")
                    phone_number = "Error"
                
                # Create record for already applied
                record = {
                    "Job Link": job_url,
                    "Status": status,
                    "Chatbot Used": chatbot_used,
                    "Phone Number": phone_number,
                    "Page": current_page,
                    "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "Retry Attempt": attempt + 1,
                    **job_metadata
                }
                return record, True
                
            except TimeoutException:
                pass

            # Check for company site redirect
            try:
                company_site_btn = WebDriverWait(driver, 3).until(
                    EC.visibility_of_element_located((By.ID, "company-site-button"))
                )
                status = "External Application Required"
                print(f"🔗 {INSTANCE_ID} External application required")
                
                record = {
                    "Job Link": job_url,
                    "Status": status,
                    "Chatbot Used": chatbot_used,
                    "Phone Number": "Not Checked",
                    "Page": current_page,
                    "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "Retry Attempt": attempt + 1,
                    **job_metadata
                }
                return record, True
                
            except TimeoutException:
                pass

            # Click apply button
            try:
                apply_btn = WebDriverWait(driver, 15).until(
                    EC.element_to_be_clickable((By.ID, "apply-button"))
                )
                apply_btn.click()
                print(f"🖱️ {INSTANCE_ID} Clicked apply button (Attempt {attempt + 1})")
                time.sleep(3)  # Wait for application process to start
                
            except TimeoutException:
                print(f"❌ {INSTANCE_ID} Apply button not found (Attempt {attempt + 1})")
                if attempt < max_retries:
                    continue
                else:
                    status = "Apply button not found"
                    break

            # Handle chatbot interaction
            status, chatbot_used = handle_chatbot(driver)
            
            # Comprehensive success verification
            final_status, is_successful = comprehensive_success_check(driver, chatbot_used)
            
            if is_successful:
                print(f"🎉 {INSTANCE_ID} APPLICATION SUCCESSFUL on attempt {attempt + 1}")
                record = {
                    "Job Link": job_url,
                    "Status": final_status,
                    "Chatbot Used": chatbot_used,
                    "Phone Number": "Not Checked",
                    "Page": current_page,
                    "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "Retry Attempt": attempt + 1,
                    **job_metadata
                }
                return record, True
            else:
                print(f"❌ {INSTANCE_ID} APPLICATION FAILED on attempt {attempt + 1}: {final_status}")
                if attempt < max_retries:
                    print(f"🔄 {INSTANCE_ID} Will retry application...")
                    time.sleep(2)
                    continue
                else:
                    status = final_status
                    break
                    
        except Exception as e:
            print(f"❌ {INSTANCE_ID} Application error on attempt {attempt + 1}: {str(e)}")
            if attempt < max_retries:
                print(f"🔄 {INSTANCE_ID} Will retry due to error...")
                time.sleep(2)
                continue
            else:
                status = f"Application Error: {str(e)}"
                break
    
    # All retries exhausted
    print(f"💔 {INSTANCE_ID} Application failed after {max_retries + 1} attempts")
    record = {
        "Job Link": job_url,
        "Status": status,
        "Chatbot Used": chatbot_used,
        "Phone Number": "Not Checked",
        "Page": current_page,
        "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "Retry Attempt": max_retries + 1,
        **job_metadata
    }
    return record, False

def main():
    # Parse command line arguments
    search_terms = None
    locations = None

    if len(sys.argv) > 1:
        search_terms = sys.argv[1]
        print(f"📝 Using custom search terms: {search_terms}")

    if len(sys.argv) > 2:
        locations = sys.argv[2]
        print(f"📍 Using custom locations: {locations}")

    print(f"🚀 Starting Naukri Automation for {get_user_name()}")
    print(f"📊 Data will be saved to: {get_excel_file()}")
    
    # Initialize coordination client and pre-populate blacklist
    global coordination_client
    coordination_client = CoordinationClient()
    
    # Pre-populate blacklist with manually specified companies
    pre_blacklist_companies = [
        "Benovymed Healthcare Private Limited"
    ]
    
    for company in pre_blacklist_companies:
        coordination_client.add_to_blacklist(company)
    
    # Print enhanced blacklist summary with analytics
    coordination_client.print_enhanced_blacklist_summary()
    print("="*60)

    try:
        driver, wait = setup_driver()
        handle_authentication(driver, wait)
        configure_search(wait, search_terms, locations)
        apply_filters(wait)

        current_page = 1
        while True:
            print(f"\n{'='*40}\nProcessing page {current_page}\n{'='*40}")
            if not process_job_page(driver, wait, current_page):
                print(f"Stopped processing at page {current_page}")
                break

            # Enhanced pagination handling
            try:
                next_btn = wait.until(EC.presence_of_element_located(
                    (By.XPATH, "//a[contains(., 'Next') and not(contains(@class, 'disabled'))]"))
                )
                if next_btn.is_enabled():
                    next_btn.click()
                    print(f"Navigated to page {current_page + 1}")
                    current_page += 1
                    # Wait for page stability
                    WebDriverWait(driver, PAGE_LOAD_TIMEOUT).until(
                        lambda d: d.execute_script("return document.readyState") == "complete"
                    )
                    time.sleep(2)  # Additional stabilization time
                else:
                    print("Next button is disabled")
                    break
            except TimeoutException:
                print("No more pages available")
                break

    except Exception as e:
        print(f"Main process error: {str(e)}")
    finally:
        # Ensure data preservation using robust Excel handling
        try:
            if applications:
                safe_append_to_excel(applications)
                print(f"\n✅ {len(applications)} job applications processed and saved")
            else:
                print("\n⚠️ No applications to save")
        except Exception as e:
            print(f"Error saving data: {str(e)}")

        if 'driver' in locals():
            driver.quit()
            print("Browser closed")

if __name__ == "__main__":
    main()
