# Naukri Automation System

A simplified, reliable job application automation system for Naukri.com using the original working logic.

## 🚀 Features

- **Simple Menu Interface**: Easy-to-use main menu for all operations
- **Original Working Logic**: Preserved and tested automation logic
- **Single Job Search**: Run automation with default search criteria
- **Multiple Instances**: Run multiple automation instances with different search terms
- **Recommendations**: Process recommended jobs from Naukri
- **Data Tracking**: Saves all applications to Excel file
- **Phone Number Extraction**: Extracts contact information from job descriptions
- **Chatbot Integration**: Handles application questions using AI

## 📁 Project Structure

```
Naukri/
├── main.py                           # Main interface - START HERE
├── naukri_automation_original.py     # Single automation script
├── run_multiple_original.py          # Multiple instances script
├── naukri_recommendations_original.py # Recommendations script
├── requirements.txt                  # Dependencies
├── job_applications.xlsx             # Output data file
├── legacy/                          # Original backup files
├── legacy_backup/                   # Complete backup
├── legacy_backup_simple/            # Simple backup
├── logs/                           # Log files
├── scripts/                        # Utility scripts
└── data/                           # Data files
```

## ⚙️ Setup

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Environment Configuration**:
   Create a `.env` file with your Naukri credentials:
   ```
   NAUKRI_EMAIL=<EMAIL>
   NAUKRI_PASSWORD=your_password
   ```

3. **Chrome Driver**:
   Ensure Chrome browser is installed. The script uses ChromeDriver.

## 🎯 Usage

### Main Interface (Recommended)
```bash
python main.py
```

This will show you a menu with options:
1. 🔍 Run Single Job Search & Application
2. 🔄 Run Multiple Job Search & Application
3. 💡 Run Recommendations Automation
4. 📊 View Job Applications Data
5. ❌ Exit

### Direct Script Execution
```bash
# Single automation
python naukri_automation_original.py

# Multiple instances
python run_multiple_original.py

# Recommendations only
python naukri_recommendations_original.py
```

## 🔧 Configuration

### Default Search Terms
- Data analytics, research analyst, Data Analyst
- Python developer, Machine Learning
- Artificial Intelligence

### Applied Filters
- **Experience**: 0-1 years
- **Salary**: 3-6 Lakhs, 6-10 Lakhs, 10-15 Lakhs
- **Location**: Noida, Gurugram, Pune, Delhi
- **Freshness**: Last 15 days

## 📊 Output

- **job_applications.xlsx**: Complete application history
  - Job Link
  - Application Status
  - Chatbot Usage
  - Phone Numbers (when available)
  - Timestamp
  - Page Number

## 🛡️ Safety Features

- ✅ Handles already applied jobs
- ✅ Manages external application redirects
- ✅ Retry mechanism for failed operations
- ✅ Data preservation on errors
- ✅ Window management for multiple tabs
- ✅ Rate limiting respect

## 🔍 Troubleshooting

1. **Login Issues**: Check `.env` file credentials
2. **Chrome Issues**: Update Chrome browser
3. **Network Issues**: Verify internet connection
4. **Missing Files**: Run from correct directory

## 📝 Notes

- All complex modular logic has been removed
- Only the original working logic is preserved
- Complete backups are available in legacy folders
- Simple, reliable, and easy to maintain

## ⚖️ Legal Notice

This tool is for educational purposes. Use responsibly and comply with Naukri.com's terms of service.
   HAS_DBS=Yes
   HAS_CRIMINAL_CONVICTIONS=No
   HAS_VALID_CERT=Yes
   ```

### Important Security Notes

- **Never commit the `.env` file to version control**
- The `.gitignore` file is configured to exclude sensitive files
- All personal data files (`.xlsx`, `.pdf`, etc.) are ignored by git
- Cookies and session files are also excluded from version control

### Usage

1. **Naukri Automation:**
   ```bash
   python naukri_automation.py
   ```

2. **Glassdoor Automation:**
   ```bash
   python glassdoor/main.py
   ```

3. **Indeed Automation:**
   ```bash
   python Indeedapplier/apply.py
   ```

### Files Structure

```
├── .env                    # Your environment variables (not tracked by git)
├── .gitignore             # Git ignore file for security
├── requirements.txt       # Python dependencies
├── naukri_automation.py   # Main Naukri automation script
├── config.py              # Configuration using environment variables
├── glassdoor/             # Glassdoor automation scripts
│   ├── main.py
│   └── config.py
└── Indeedapplier/         # Indeed automation scripts
    ├── apply.py
    └── config.py
```

### Troubleshooting

1. **Missing environment variables:** Make sure all required variables are set in your `.env` file
2. **Import errors:** Ensure all dependencies are installed with `pip install -r requirements.txt`
3. **ChromeDriver issues:** The script uses webdriver-manager to handle ChromeDriver automatically

### Contributing

1. Never commit sensitive data
2. Always use environment variables for credentials
3. Test your changes before committing
4. Update documentation when adding new features

### License

This project is for educational purposes only. Please ensure you comply with the terms of service of the job portals you're automating.
