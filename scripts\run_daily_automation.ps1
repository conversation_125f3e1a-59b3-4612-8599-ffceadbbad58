# PowerShell script to run Naukri automation
$scriptPath = "c:\Users\<USER>\OneDrive - BENNETT UNIVERSITY\Documents\Naukri"
$logFile = "$scriptPath\automation_log.txt"

# Change to script directory
Set-Location $scriptPath

# Log start time
$startTime = Get-Date
"Starting Naukri Automation at $startTime" | Out-File $logFile -Append

try {
    # Run the Python script
    python run_multiple.py
    
    # Log success
    $endTime = Get-Date
    "Automation completed successfully at $endTime" | Out-File $logFile -Append
}
catch {
    # Log error
    $errorTime = Get-Date
    "Error occurred at $errorTime : $($_.Exception.Message)" | Out-File $logFile -Append
}
