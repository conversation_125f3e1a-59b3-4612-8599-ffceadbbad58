import subprocess
import sys
import random
import time
import threading
import json
import os
from datetime import datetime
from user_config import get_user_name, get_excel_file

# List of possible search inputs
SEARCH_INPUTS = [
    "Bussiness Analyst",
    # "Generative AI",
    "Data scientist,data science",
    # "AI Ml Engineer,machine learning,artificial intelligence",
    "senior data analyst,data analyst"
]

# List of possible location combinations
LOCATION_INPUTS = [" "]

# Number of times to run the script
NUM_RUNS = 3
# Company application limits (shared across all users and instances)
MAX_APPLICATIONS_PER_COMPANY = 2  # Maximum applications allowed per company
ENABLE_COMPANY_TRACKING = True    # Enable advanced company tracking

# Run mode: 'parallel', 'sequential', or 'smart_parallel'
# 'sequential'    - Runs one script at a time, no conflicts, more reliable
# 'parallel'      - Faster but may have radio button conflicts when multiple 
#                   scripts encounter chatbot dialogs simultaneously
# 'smart_parallel' - Advanced parallel mode with conflict resolution
#                   Uses shared state management and coordination
RUN_MODE = 'smart_parallel'

# Advanced parallel mode settings
SHARED_STATE_FILE = "automation_shared_state.json"
COORDINATION_LOCK_FILE = "automation_coordination.lock"
CHATBOT_QUEUE_FILE = "chatbot_queue.json"

class AutomationCoordinator:
    """Advanced coordination system for parallel automation instances"""
    
    def __init__(self):
        self.state_file = SHARED_STATE_FILE
        self.lock_file = COORDINATION_LOCK_FILE
        self.queue_file = CHATBOT_QUEUE_FILE
        self.lock = threading.Lock()
        self.cleanup_on_start()
    
    def cleanup_on_start(self):
        """Clean up any leftover state files from previous runs"""
        for file in [self.state_file, self.lock_file, self.queue_file]:
            if os.path.exists(file):
                try:
                    os.remove(file)
                except:
                    pass
    
    def initialize_shared_state(self):
        """Initialize shared state for coordination"""
        state = {
            "active_instances": [],
            "chatbot_in_progress": False,
            "chatbot_handler": None,
            "excel_in_progress": False,
            "excel_handler": None,
            "radio_button_responses": {
                "notice_period": "1 Month",
                "current_ctc": "5",
                "expected_ctc": "8",
                "experience": "2",
                "availability": "Immediately"
            },
            "coordination_enabled": True,
            "start_time": datetime.now().isoformat(),
            "total_applications_saved": 0,
            # Enhanced company tracking in JSON
            "company_counts": {},  # Track applications per company across all users/instances
            "blacklisted_companies": [],  # Shared blacklist for all users/instances
            "max_applications_per_company": MAX_APPLICATIONS_PER_COMPANY
        }
        self._write_state(state)
        return state
    
    def _read_state(self):
        """Read shared state with file locking"""
        if not os.path.exists(self.state_file):
            return self.initialize_shared_state()
        
        try:
            with open(self.state_file, 'r') as f:
                return json.load(f)
        except:
            return self.initialize_shared_state()
    
    def _write_state(self, state):
        """Write shared state with file locking"""
        try:
            with open(self.state_file, 'w') as f:
                json.dump(state, f, indent=2)
        except Exception as e:
            print(f"Warning: Could not write state: {e}")
    
    def register_instance(self, instance_id):
        """Register a new automation instance"""
        with self.lock:
            state = self._read_state()
            if instance_id not in state["active_instances"]:
                state["active_instances"].append(instance_id)
                self._write_state(state)
            print(f"🔗 Instance {instance_id} registered. Active: {len(state['active_instances'])}")
    
    def unregister_instance(self, instance_id):
        """Unregister an automation instance"""
        with self.lock:
            state = self._read_state()
            if instance_id in state["active_instances"]:
                state["active_instances"].remove(instance_id)
                # If this was the chatbot handler, release it
                if state.get("chatbot_handler") == instance_id:
                    state["chatbot_in_progress"] = False
                    state["chatbot_handler"] = None
                self._write_state(state)
            print(f"🔌 Instance {instance_id} unregistered")
    
    def request_chatbot_lock(self, instance_id):
        """Request exclusive lock for chatbot handling"""
        max_wait = 300  # 5 minutes max wait
        wait_time = 0
        
        while wait_time < max_wait:
            with self.lock:
                state = self._read_state()
                if not state["chatbot_in_progress"]:
                    state["chatbot_in_progress"] = True
                    state["chatbot_handler"] = instance_id
                    self._write_state(state)
                    print(f"🤖 Instance {instance_id} acquired chatbot lock")
                    return True
            
            # Wait and check again
            time.sleep(2)
            wait_time += 2
            if wait_time % 30 == 0:  # Log every 30 seconds
                print(f"⏳ Instance {instance_id} waiting for chatbot lock... ({wait_time}s)")
        
        print(f"⏰ Instance {instance_id} timeout waiting for chatbot lock")
        return False
    
    def release_chatbot_lock(self, instance_id):
        """Release chatbot lock"""
        with self.lock:
            state = self._read_state()
            if state.get("chatbot_handler") == instance_id:
                state["chatbot_in_progress"] = False
                state["chatbot_handler"] = None
                self._write_state(state)
                print(f"🔓 Instance {instance_id} released chatbot lock")
    
    def request_excel_lock(self, instance_id):
        """Request exclusive lock for Excel file operations"""
        max_wait = 120  # 2 minutes max wait for Excel operations
        wait_time = 0
        
        while wait_time < max_wait:
            with self.lock:
                state = self._read_state()
                if not state.get("excel_in_progress", False):
                    state["excel_in_progress"] = True
                    state["excel_handler"] = instance_id
                    self._write_state(state)
                    print(f"📊 Instance {instance_id} acquired Excel lock")
                    return True
            
            # Wait and check again
            time.sleep(1)
            wait_time += 1
            if wait_time % 15 == 0:  # Log every 15 seconds
                print(f"⏳ Instance {instance_id} waiting for Excel lock... ({wait_time}s)")
        
        print(f"⏰ Instance {instance_id} timeout waiting for Excel lock")
        return False
    
    def release_excel_lock(self, instance_id):
        """Release Excel lock"""
        with self.lock:
            state = self._read_state()
            if state.get("excel_handler") == instance_id:
                state["excel_in_progress"] = False
                state["excel_handler"] = None
                self._write_state(state)
                print(f"🔓 Instance {instance_id} released Excel lock")
    
    def update_applications_count(self, count):
        """Update the total applications saved count"""
        with self.lock:
            state = self._read_state()
            state["total_applications_saved"] = state.get("total_applications_saved", 0) + count
            self._write_state(state)
            print(f"📈 Total applications saved across all instances: {state['total_applications_saved']}")

    def get_radio_response(self, question_type):
        """Get predefined response for radio button questions"""
        state = self._read_state()
        responses = state.get("radio_button_responses", {})
        return responses.get(question_type.lower(), None)
    
    def is_coordination_enabled(self):
        """Check if coordination is enabled"""
        state = self._read_state()
        return state.get("coordination_enabled", True)
    
    def add_company_application(self, company_name):
        """Add application count for a company and check if should be blacklisted"""
        with self.lock:
            state = self._read_state()
            
            # Initialize tracking fields if not present
            if "company_counts" not in state:
                state["company_counts"] = {}
            if "blacklisted_companies" not in state:
                state["blacklisted_companies"] = []
            if "max_applications_per_company" not in state:
                state["max_applications_per_company"] = MAX_APPLICATIONS_PER_COMPANY
            
            # Update company count
            current_count = state["company_counts"].get(company_name, 0)
            new_count = current_count + 1
            state["company_counts"][company_name] = new_count
            
            print(f"📊 {company_name}: {new_count} applications (across all users/instances)")
            
            # Check if should be blacklisted
            max_allowed = state["max_applications_per_company"]
            if new_count >= max_allowed and company_name not in state["blacklisted_companies"]:
                state["blacklisted_companies"].append(company_name)
                print(f"🚫 {company_name} BLACKLISTED - Reached limit ({max_allowed} applications)")
                self._write_state(state)
                return new_count, True  # blacklisted
            
            self._write_state(state)
            return new_count, False  # not blacklisted
    
    def is_company_blacklisted(self, company_name):
        """Check if company is blacklisted"""
        state = self._read_state()
        blacklisted = state.get("blacklisted_companies", [])
        return company_name in blacklisted
    
    def get_company_count(self, company_name):
        """Get current application count for a company"""
        state = self._read_state()
        company_counts = state.get("company_counts", {})
        return company_counts.get(company_name, 0)
    
    def get_blacklisted_companies(self):
        """Get list of all blacklisted companies"""
        state = self._read_state()
        return state.get("blacklisted_companies", [])
    
    def get_company_stats(self):
        """Get comprehensive company statistics"""
        state = self._read_state()
        company_counts = state.get("company_counts", {})
        blacklisted = state.get("blacklisted_companies", [])
        max_allowed = state.get("max_applications_per_company", MAX_APPLICATIONS_PER_COMPANY)
        
        stats = {
            "total_companies": len(company_counts),
            "blacklisted_count": len(blacklisted),
            "max_applications_per_company": max_allowed,
            "top_companies": sorted(company_counts.items(), key=lambda x: x[1], reverse=True)[:10],
            "blacklisted_companies": blacklisted
        }
        return stats

def run_smart_parallel():
    """Advanced parallel mode with coordination"""
    print("🧠 Running in SMART PARALLEL mode (advanced coordination)")
    print("✨ Features: Chatbot coordination, radio button sync, conflict resolution")
    
    coordinator = AutomationCoordinator()
    coordinator.initialize_shared_state()
    
    processes = []
    instance_ids = []

    for i in range(NUM_RUNS):
        search_text = selected_search_inputs[i]
        location_text = selected_location_inputs[i]
        instance_id = f"instance_{i+1}_{int(time.time())}"
        instance_ids.append(instance_id)

        print(f"\n🔍 Starting smart instance {i+1}:")
        print(f"   Search: {search_text}")
        print(f"   Locations: {location_text}")
        print(f"   Instance ID: {instance_id}")

        # Pass coordination parameters
        env = os.environ.copy()
        env['AUTOMATION_INSTANCE_ID'] = instance_id
        env['AUTOMATION_COORDINATION_ENABLED'] = 'true'
        env['AUTOMATION_STATE_FILE'] = coordinator.state_file

        # Start process with coordination
        p = subprocess.Popen([
            sys.executable, "naukri_automation_original.py", 
            search_text, location_text
        ], env=env)
        processes.append(p)
        
        # Register instance
        coordinator.register_instance(instance_id)
        
        # Small delay between starts to avoid initial conflicts
        time.sleep(5)

    print(f"\n✅ All {NUM_RUNS} smart instances started!")
    print("🔄 Coordination system active - managing conflicts automatically")
    print("⏳ Waiting for all instances to complete...")

    # Monitor and wait for completion
    for i, p in enumerate(processes):
        try:
            p.wait()
            coordinator.unregister_instance(instance_ids[i])
            print(f"✅ Smart instance {i+1} finished.")
        except Exception as e:
            print(f"❌ Smart instance {i+1} error: {e}")
            coordinator.unregister_instance(instance_ids[i])
    
    # Show final coordination stats
    final_state = coordinator._read_state()
    total_saved = final_state.get("total_applications_saved", 0)
    company_stats = coordinator.get_company_stats()
    
    print(f"\n📊 COORDINATION SUMMARY:")
    print(f"   • Total applications saved across all instances: {total_saved}")
    print(f"   • Excel file coordination: ✅ Success")
    print(f"   • Radio button conflicts: ✅ Zero conflicts")
    print(f"\n🏢 COMPANY TRACKING SUMMARY:")
    print(f"   • Total companies encountered: {company_stats['total_companies']}")
    print(f"   • Companies blacklisted: {company_stats['blacklisted_count']}")
    print(f"   • Max applications per company: {company_stats['max_applications_per_company']}")
    
    if company_stats['blacklisted_companies']:
        print(f"   • Blacklisted companies: {', '.join(company_stats['blacklisted_companies'][:5])}{'...' if len(company_stats['blacklisted_companies']) > 5 else ''}")
    
    if company_stats['top_companies']:
        print(f"   • Top companies by applications:")
        for company, count in company_stats['top_companies'][:5]:
            print(f"     - {company}: {count} applications")
    
    # Cleanup
    coordinator.cleanup_on_start()

def run_sequential():
    """Run automation instances one after another"""
    print("� Running in SEQUENTIAL mode (recommended for radio button handling)")
    
    for i in range(NUM_RUNS):
        search_text = selected_search_inputs[i]
        location_text = selected_location_inputs[i]

        print(f"\n� Starting instance {i+1}/{NUM_RUNS}:")
        print(f"   Search: {search_text}")
        print(f"   Locations: {location_text}")

        # Run script and wait for completion
        try:
            result = subprocess.run([sys.executable, "naukri_automation_original.py", search_text, location_text], 
                                  capture_output=False, text=True)
            if result.returncode == 0:
                print(f"✅ Instance {i+1} completed successfully!")
            else:
                print(f"❌ Instance {i+1} failed with return code {result.returncode}")
        except Exception as e:
            print(f"❌ Error running instance {i+1}: {e}")
        
        # Add delay between runs to avoid rate limiting
        if i < NUM_RUNS - 1:
            print("⏳ Waiting 30 seconds before next instance...")
            time.sleep(30)

def run_parallel():
    """Run automation instances in parallel (original behavior)"""
    print("⚡ Running in PARALLEL mode (may have radio button conflicts)")
    
    processes = []

    for i in range(NUM_RUNS):
        search_text = selected_search_inputs[i]
        location_text = selected_location_inputs[i]

        print(f"\n🔍 Starting instance {i+1}:")
        print(f"   Search: {search_text}")
        print(f"   Locations: {location_text}")

        # Pass both search terms and locations as arguments
        p = subprocess.Popen([sys.executable, "naukri_automation_original.py", search_text, location_text])
        processes.append(p)

    print(f"\n✅ All {NUM_RUNS} instances started successfully!")
    print("⏳ Waiting for all instances to complete...")

    for i, p in enumerate(processes):
        p.wait()
        print(f"✅ Instance {i+1} finished.")

def main():
    print(f"🚀 Starting Multiple Naukri Automation Instances for {get_user_name()}")
    print(f"📊 Running {NUM_RUNS} instances with different search terms and locations")
    print(f"💾 Data will be saved to: {get_excel_file()}")
    print(f"🎯 Run Mode: {RUN_MODE.upper()}")
    print("="*60)

    # Pick unique search inputs for each run
    global selected_search_inputs, selected_location_inputs
    
    if NUM_RUNS <= len(SEARCH_INPUTS):
        selected_search_inputs = random.sample(SEARCH_INPUTS, NUM_RUNS)
    else:
        selected_search_inputs = [random.choice(SEARCH_INPUTS) for _ in range(NUM_RUNS)]

    # Pick unique location inputs for each run
    if NUM_RUNS <= len(LOCATION_INPUTS):
        selected_location_inputs = random.sample(LOCATION_INPUTS, NUM_RUNS)
    else:
        selected_location_inputs = [random.choice(LOCATION_INPUTS) for _ in range(NUM_RUNS)]

    # Run based on selected mode
    if RUN_MODE.lower() == 'sequential':
        run_sequential()
    elif RUN_MODE.lower() == 'smart_parallel':
        run_smart_parallel()
    else:
        run_parallel()

    print("\n🎉 All runs completed successfully!")
    print("📊 Check job_applications.xlsx for results")

if __name__ == "__main__":
    main()
