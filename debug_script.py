#!/usr/bin/env python3
"""Debug script to test Chrome driver and basic functionality"""

print("🔧 Starting debug test...")

try:
    print("1. Testing imports...")
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from user_config import get_user_name
    print("✅ All imports successful")
    
    print("2. Testing user config...")
    user = get_user_name()
    print(f"✅ Current user: {user}")
    
    print("3. Testing Chrome options...")
    options = webdriver.ChromeOptions()
    options.add_argument("--start-maximized")
    options.add_argument("--disable-notifications")
    options.add_argument("--disable-popup-blocking")
    print("✅ Chrome options created")
    
    print("4. Creating Chrome driver...")
    driver = webdriver.Chrome(options=options)
    print("✅ Chrome driver created successfully")
    
    print("5. Testing navigation...")
    driver.get("https://www.google.com")
    print(f"✅ Successfully navigated to: {driver.current_url}")
    
    print("6. Closing driver...")
    driver.quit()
    print("✅ Debug test completed successfully!")
    
except Exception as e:
    print(f"❌ Debug test failed: {str(e)}")
    import traceback
    traceback.print_exc()
